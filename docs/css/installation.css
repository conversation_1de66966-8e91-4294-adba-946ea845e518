/* Installation / How It Works Section */
.how-it-works {
    padding: var(--section-padding);
    background: var(--light-color);
}

.steps {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-top: 50px;
}

.step {
    text-align: left;
    flex: 1;
    min-width: 250px;
    margin-bottom: 30px;
    padding: 0 20px;
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.step-number {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 60px;
    height: 60px;
    background: var(--primary-color);
    color: white;
    font-size: 1.5rem;
    font-weight: 700;
    border-radius: 50%;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
    text-align: left;
}

.step h3 {
    margin-bottom: 15px;
    font-size: 1.3rem;
}

/* Tabs Styles */
.tabs {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 30px;
    border-bottom: 2px solid #eee;
}

.tab-btn {
    padding: 12px 24px;
    background: transparent;
    border: none;
    cursor: pointer;
    font-weight: 600;
    color: #777;
    position: relative;
    transition: all 0.3s ease;
}

.tab-btn:hover {
    color: var(--primary-color);
}

.tab-btn.active {
    color: var(--primary-color);
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--primary-color);
}

.tab-content {
    display: none;
    padding: 20px 0;
}

.tab-content.active {
    display: block;
}

@media (max-width: 992px) {
    .steps {
        flex-direction: column;
    }
    
    .step {
        margin-bottom: 50px;
    }
}
