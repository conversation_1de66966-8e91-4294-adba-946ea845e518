/* Footer */
footer {
    background: var(--dark-color);
    color: var(--light-text);
    padding: 60px 0 20px;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 40px;
    margin-bottom: 60px;
}

.footer-column {
    flex: 1;
    min-width: 250px;
}

.footer-column h3 {
    font-size: 1.3rem;
    margin-bottom: 20px;
    font-weight: 500;
    color: var(--secondary-color);
}

.footer-column ul {
    list-style: none;
}

.footer-column ul li {
    margin-bottom: 10px;
}

.footer-column ul li a {
    color: var(--light-text);
    opacity: 0.8;
    transition: all 0.3s ease;
}

.footer-column ul li a:hover {
    opacity: 1;
    color: var(--secondary-color);
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.footer-logo h3 {
    margin-top: 18px;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: var(--light-text);
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
}

.copyright {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.9rem;
    opacity: 0.7;
}
