/* Demo Section */
.demo {
    padding: var(--section-padding);
    background: #fff;
}

.demo-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 50px;
    flex-wrap: wrap;
}

.demo-text {
    flex: 1;
    min-width: 300px;
}

.demo-text h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: var(--primary-color);
}

.demo-text p {
    margin-bottom: 20px;
}

.demo-image {
    flex: 1;
    min-width: 300px;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.video-container {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    height: 0;
    overflow: hidden;
}

.video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 0;
}

@media (max-width: 992px) {
    .demo-content {
        flex-direction: column;
    }
}
