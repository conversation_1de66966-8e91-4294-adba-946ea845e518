/* Header */
header {
    background: var(--dark-color);
    color: var(--light-text);
    padding: 20px 0;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    left: 0;
    right: 0;
    box-sizing: border-box;
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    box-sizing: border-box;
}

.header-right {
    display: flex;
    align-items: center;
}

.header-cta-btn {
    display: inline-block;
    background: #0078D7;
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.8rem;
    margin-left: 15px;
    border: 1px solid #0078D7;
    text-decoration: none;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.header-cta-btn:hover {
    background: #0164b6;
    border-color: #0164b6;
    box-shadow: 0 2px 8px rgba(88, 101, 242, 0.4);
}

.mobile-menu-btn {
    display: none;
    cursor: pointer;
    margin-left: 15px;
}

.logo {
    font-size: 1.8rem;
    font-weight: 500;
    color: var(--light-text);
    display: flex;
    align-items: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.logo svg {
    margin-right: 10px;
}

nav {
    position: relative;
    overflow: visible;
}

nav ul {
    display: flex;
    list-style: none;
    position: relative;
    z-index: 1001;
}

nav ul li {
    margin-left: 30px;
    position: relative;
}

nav ul li a {
    color: var(--light-text);
    font-weight: 500;
    position: relative;
    padding: 5px 0;
}

nav ul li a:hover {
    color: var(--secondary-color);
}

nav ul li a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--secondary-color);
    transition: width 0.3s ease;
}

nav ul li a:hover::after {
    width: 100%;
}



/* Media queries for responsive header */
@media (max-width: 768px) {
    header {
        padding: 15px 0;
    }
    
    .header-container {
        padding: 0 10px;
    }
    
    .logo {
        font-size: 1.5rem;
        white-space: nowrap;
        max-width: 80%;
    }
    
    nav {
        position: fixed;
        top: 80px;
        left: 0;
        width: 100%;
        background: var(--dark-color);
        padding: 20px 0;
        transform: translateY(-150%);
        transition: transform 0.3s ease;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        display: none;
    }
    
    nav.active {
        transform: translateY(0);
        display: block;
    }
    
    nav ul {
        flex-direction: column;
        align-items: center;
        padding: 0;
        margin: 0;
    }
    
    nav ul li {
        margin: 10px 0;
    }
    
    .mobile-menu-btn {
        display: block;
    }
}

@media (max-width: 576px) {
    .logo {
        font-size: 1.2rem;
    }
    
    .logo img {
        margin-right: 5px;
    }
}
