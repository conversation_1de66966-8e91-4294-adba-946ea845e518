package com.ruoyi.system.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.ruoyi.common.core.web.domain.BaseEntity;

import java.util.Date;

/**
 * 测试信息对象 sys_test
 *
 * <AUTHOR>
 * @date 2025-01-16
 */

@Data
@Schema(name="测试信息对象", description="测试信息对象 sys_test")
public class SysTest
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 名称 */
    @Schema(name = "name",description = "名称")
    private String name;

    /** 删除1 不删除0 */
    @Schema(name = "isDelete",description = "删除1 不删除0")
    private Boolean isDelete;

    private Date createTime;


    private Date updateTime;


}
