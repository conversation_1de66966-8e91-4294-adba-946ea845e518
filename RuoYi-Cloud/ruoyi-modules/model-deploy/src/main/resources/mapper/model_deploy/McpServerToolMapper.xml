<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.model_deploy.mapper.McpServerToolMapper">

    <resultMap type="com.ruoyi.model_deploy.domain.McpServerTool" id="McpServerToolResult">
        <result property="id" column="id"/>
        <result property="serverName" column="server_name"/>
        <result property="params" column="params"/>
        <result property="createdAt" column="created_at"/>
        <result property="createdBy" column="created_by"/>
        <result property="updateAt" column="update_at"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDeleted" column="is_deleted"/>
    </resultMap>

    <sql id="selectMcpServerToolVo">
        select id,
               server_name,
               params,
               created_at,
               created_by,
               update_at,
               update_by,
               is_deleted
        from mcp_server_tool
    </sql>

    <select id="selectMcpServerToolByName"
            resultType="com.ruoyi.model_deploy.domain.vo.McpToolVO">
        <include refid="selectMcpServerToolVo"/>
        where is_deleted = 0
        <if test="serverName != null  and serverName != ''">and server_name = #{serverName}</if>
    </select>

    <select id="selectMcpServerToolByName2"
            resultType="com.ruoyi.model_deploy.domain.vo.McpToolVO">
        <include refid="selectMcpServerToolVo"/>
        where is_deleted = 0
        <if test="serverName != null  and serverName != ''">and server_name = #{serverName}</if>
        and id != #{id}
    </select>

    <select id="selectMcpServerToolList"
            parameterType="com.ruoyi.model_deploy.domain.request.QueryMcpToolRequest"
            resultType="com.ruoyi.model_deploy.domain.vo.McpToolVO">
        <include refid="selectMcpServerToolVo"/>
        where is_deleted = 0
        <if test="serverName != null  and serverName != ''">and server_name = #{serverName}</if>
    </select>

    <select id="selectMcpServerToolById" parameterType="Long" resultType="com.ruoyi.model_deploy.domain.vo.McpToolVO">
        <include refid="selectMcpServerToolVo"/>
        where id = #{id} and is_deleted = 0
    </select>

    <insert id="insertMcpServerTool"
            parameterType="com.ruoyi.model_deploy.domain.request.OptMcpToolRequest"
            useGeneratedKeys="true"
            keyProperty="id">
        insert into mcp_server_tool
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serverName != null">server_name,</if>
            <if test="params != null">params,</if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serverName != null">#{serverName},</if>
            <if test="params != null">#{params},</if>
        </trim>
    </insert>

    <update id="updateMcpServerTool" parameterType="com.ruoyi.model_deploy.domain.request.OptMcpToolRequest">
        update mcp_server_tool
        <trim prefix="SET" suffixOverrides=",">
            <if test="serverName != null">server_name = #{serverName},</if>
            <if test="params != null">params = #{params},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteMcpServerToolByName" parameterType="String">
        update mcp_server_tool
        set is_deleted = 1
        where server_name = #{serverName}
    </update>

</mapper>