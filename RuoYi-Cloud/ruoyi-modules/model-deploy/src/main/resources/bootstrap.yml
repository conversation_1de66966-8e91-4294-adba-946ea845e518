# Tomcat
server:
  port: 19209

# Spring
spring:
  mvc:
    path-match:
      matching-strategy: ant_path_matcher
  application:
    # 应用名称
    name: model-deploy
  profiles:
    # 环境配置
    active: dev
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10GB
      # 设置总上传的文件大小
      max-request-size: 20GB
  cloud:
    nacos:
      username: nacos
      password: nacosNiii
      discovery:
        # 服务注册地址
        server-addr: *************:8848
      config:
        username: nacos
        password: nacosNiii
        # 配置中心地址
        server-addr: *************:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}

# Minio配置
minio:
  url: http://*************:9000
  accessKey: Seb3Y7qiDcNsr6f0yr4U
  secretKey: 8Xm3Sc7aciEv9UfOoIGqBCkmk7gv45FPInKY7iP9
  bucketName: models-bucket
  expirySeconds: 7200
  # 连接超时时间（单位：秒）
  connectTimeout: 300
  # 写超时时间（单位：秒）
  writeTimeout: 300
  # 读超时时间（单位：秒）
  readTimeout: 300
  # 分片上传阈值（单位：MB）
  multipartThreshold: 100
  # 分片大小（单位：MB）
  multipartChunksize: 100
  # 最大上传线程数
  maxUploadThreads: 10
