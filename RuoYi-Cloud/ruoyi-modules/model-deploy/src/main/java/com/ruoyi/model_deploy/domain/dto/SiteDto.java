package com.ruoyi.model_deploy.domain.dto;




import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "基础信息DTO")
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SiteDto {

    @Schema(description = "名称")
    private String title;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "语言")
    private String defaultLanguage;

    @Schema(description = "聊天颜色主题")
    private String chatColorTheme;

    @Schema(description = "聊天颜色主题")
    private Boolean chatColorThemeInverted;

    private Boolean promptPublic;

    @Schema(description = "版权")
    private String copyright;

    @Schema(description = "隐私政策")
    private String privacyPolicy;

    @Schema(description = "自定义免责声明")
    private String customDisclaimer;

    @Schema(description = "图表类型")
    private String iconType;

    @Schema(description = "图标")
    private String icon;

    @Schema(description = "图标背景色")
    private String iconBackground;

    @Schema(description = "工作流详情")
    private Boolean showWorkflowSteps;

    @Schema(description = "使用WebApp图标替换")
    private Boolean useIconAsAnswerIcon;

//    @Schema(description = "单点登录认证")
//    private Boolean enableSso;
}

