package com.ruoyi.model_deploy.service;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.utils.http.HttpUtils;
import com.ruoyi.model_deploy.domain.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.junit.platform.commons.util.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 知识库
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RefreshScope
public class KnowledgeService {

    @Value("${knowledge.api-url}")
    private String baseUrl;
    @Value("${knowledge.api-key}")
    private String apiKey;
    @Value("${knowledge.console-url}")
    private String consoleUrl;
    @Value("${knowledge.console-token}")
    private String consoleToken;

    /**
     * 创建空知识库
     */
    public JSONObject add(JSONObject dto) {
        return executeRequestWithApiKey("POST", "/datasets", dto, null);
    }

    /**
     * 查询知识库列表
     */
    public JSONObject list(KnowledgeListDto dto) {
        Map<String, Object> params = new HashMap<>();
        params.put("page", dto.getPage());
        params.put("limit", dto.getLimit());
        if (Strings.isNotBlank(dto.getKeyword())) {
            params.put("keyword", dto.getKeyword());
        }
        return executeRequestWithApiKey("GET", "/datasets", null, params);
    }

    /**
     * 删除知识库
     */
    public JSONObject remove(String datasetId) {
        return executeRequestWithApiKey("DELETE", "/datasets/" + datasetId, new JSONObject(), null);
    }

    /**
     * 新增分段
     */
    public JSONObject addSegments(String datasetId, String documentId, AddSegmentsDto dto) {
        String path = String.format("/datasets/%s/documents/%s/segments", datasetId, documentId);
        return executeRequestWithApiKey("POST", path, JSONObject.from(dto), null);
    }

    /**
     * 查询分段
     */
    public JSONObject getSegments(String datasetId, String documentId, String keyword, String enabled, Integer page, Integer limit) {
        String path = String.format("/datasets/%s/documents/%s/segments", datasetId, documentId);
        Map<String, Object> params = new HashMap<>();
        params.put("enabled", "all");
        params.put("page", page);
        params.put("limit", limit);
        if (StringUtils.isNotBlank(keyword)) {
            params.put("keyword", keyword);
        }
        if (StringUtils.isNotBlank(enabled)) {
            params.put("enabled", enabled);
        }
        return executeRequestWithConsole("GET", path, null, params);
    }

    /**
     * 删除分段
     */
    public JSONObject delSegments(String datasetId, String documentId, String segmentId) {
        String path = String.format("/datasets/%s/documents/%s/segments/%s", datasetId, documentId, segmentId);
        return executeRequestWithApiKey("DELETE", path, new JSONObject(), null);
    }

    /**
     * 更新分段
     */
    public JSONObject updateSegments(String datasetId, String documentId, String segmentId, AddSegmentsDto dto) {
        String path = String.format("/datasets/%s/documents/%s/segments/%s", datasetId, documentId, segmentId);
        return executeRequestWithApiKey("POST", path, JSONObject.from(dto), null);
    }

    /**
     * 更新文档分段状态
     */
    public JSONObject updateSegmentsStatus(String datasetId, String documentId, String action, String segmentId) {
        String path = String.format("/datasets/%s/documents/%s/segment/%s?segment_id=%s", datasetId, documentId, action, segmentId);
        return executeRequestWithConsole("PATCH", path, new JSONObject(), null);
    }

    /**
     * 更新知识库设置
     */
    public JSONObject updateSettings(String datasetId, KnowledgeSettingsDto dto) {
        String path = "/datasets/" + datasetId;
        return executeRequestWithConsole("PATCH", path, JSONObject.from(dto), null);
    }

    /**
     * 查询模型
     */
    public JSONObject getModels(String modelType) {
        String path = "/workspaces/current/models/model-types/".concat(modelType);
        Map<String, Object> params = new HashMap<>();
        return executeRequestWithConsole("GET", path, null, params);
    }

    /**
     * 查询工作流参数规则
     */
    public JSONObject getParameterRules(String provider, String model) {
        String path = String.format("/workspaces/current/model-providers/%s/models/parameter-rules", provider);
        Map<String, Object> params = new HashMap<>();
        params.put("model", model);
        return executeRequestWithConsole("GET", path, null, params);
    }

    /**
     * 执行HTTP请求的通用方法
     *
     * @param method HTTP方法
     * @param path   请求路径
     * @param body   请求体
     * @param params 查询参数
     * @return 响应结果
     */
    private JSONObject executeRequestWithApiKey(String method, String path, Object body, Map<String, Object> params) {
        return HttpUtils.executeAuthRequest(method, baseUrl, path, body, params, apiKey);
    }

    private JSONObject executeRequestWithConsole(String method, String path, Object body, Map<String, Object> params) {
        return HttpUtils.executeAuthRequest(method, consoleUrl, path, body, params, consoleToken);
    }

    /**
     * 获取应用列表
     */
    public JSONObject getApps() {
        String path = "/explore/apps";
        Map<String, Object> params = new HashMap<>();
        return executeRequestWithConsole("GET", path, null, params);
    }

    /**
     * 获取应用详情
     */
    public JSONObject getAppDetail(String appId) {
        String path = String.format("/explore/apps/%s", appId);
        Map<String, Object> params = new HashMap<>();
        return executeRequestWithConsole("GET", path, null, params);
    }

    /**
     * 导入应用
     */
    public JSONObject importApp(ImportAppDto dto) {
        String path = "/apps/imports";
        JSONObject req = new JSONObject();
        if (dto.getMode().equals("yaml-content")) {
            req.put("yaml_content", dto.getYamlContent());
            if (StringUtils.isNotBlank(dto.getDescription())) {
                req.put("description", dto.getDescription());
            }
            if (dto.getIcon() != null) {
                req.put("icon", dto.getIcon());
            }
            if (dto.getIconType() != null) {
                req.put("icon_type", dto.getIconType());
            }
            if (dto.getIconBackground() != null) {
                req.put("icon_background", dto.getIconBackground());
            }
            if (StringUtils.isNotBlank(dto.getName())) {
                req.put("name", dto.getName());
            }
        } else if (dto.getMode().equals("yaml-url")) {
            req.put("yaml_url", dto.getYamlUrl());
        }
        return executeRequestWithConsole("POST", path, req, null);
    }

    /**
     * 创建应用
     */
    public JSONObject createApp(CreateAppDto dto) {
        String path = "/apps";
        JSONObject req = JSONObject.from(dto);
        if (dto.getIconType() != null) {
            req.put("icon_type", dto.getIconType());
        }
        if (dto.getIconBackground() != null) {
            req.put("icon_background", dto.getIconBackground());
        }
        return executeRequestWithConsole("POST", path, req, null);
    }

    /**
     * 编辑应用信息
     */
    public JSONObject updateApp(String appId, CreateAppDto dto) {
        String path = String.format("/apps/%s", appId);
        JSONObject req = JSONObject.from(dto);
        if (dto.getIconType() != null) {
            req.put("icon_type", dto.getIconType());
        }
        if (dto.getIconBackground() != null) {
            req.put("icon_background", dto.getIconBackground());
        }
        if (dto.getUseIconAsAnswerIcon() != null) {
            req.put("use_icon_as_answer_icon", dto.getUseIconAsAnswerIcon());
        }
        return executeRequestWithConsole("PUT", path, req, null);
    }

    /**
     * 查询应用列表
     */
    public JSONObject getAppList(Integer page, Integer limit, String name, Boolean isCreatedByMe, String mode, String tagIds) {
        String path = "/apps";
        Map<String, Object> params = new HashMap<>();
        params.put("page", page);
        params.put("limit", limit);
        if (StringUtils.isNotBlank(name)) {
            params.put("name", name);
        }
        if (isCreatedByMe != null) {
            params.put("is_created_by_me", isCreatedByMe);
        }
        if (StringUtils.isNotBlank(mode)) {
            params.put("mode", mode);
        }
        if (StringUtils.isNotBlank(tagIds)) {
            params.put("tag_ids", tagIds);
        }
        return executeRequestWithConsole("GET", path, null, params);
    }

    /**
     * 获取应用详情配置
     */
    public JSONObject getAppConfiguration(String appId) {
        String path = String.format("/apps/%s", appId);
        return executeRequestWithConsole("GET", path, null, null);
    }

    /**
     * 删除应用
     */
    public JSONObject deleteApp(String appId) {
        String path = String.format("/apps/%s", appId);
        return executeRequestWithConsole("DELETE", path, null, null);
    }

    /**
     * 复制应用
     */
    public JSONObject copyApp(String appId, CreateAppDto dto) {
        String path = String.format("/apps/%s/copy", appId);
        JSONObject req = JSONObject.from(dto);
        if (dto.getIconType() != null) {
            req.put("icon_type", dto.getIconType());
        }
        if (dto.getIconBackground() != null) {
            req.put("icon_background", dto.getIconBackground());
        }
        return executeRequestWithConsole("POST", path, req, null);
    }

    /**
     * 导出应用配置
     */
    public JSONObject exportApp(String appId, Boolean includeSecret) {
        String path = String.format("/apps/%s/export", appId);
        Map<String, Object> params = new HashMap<>();
        params.put("include_secret", includeSecret);
        return executeRequestWithConsole("GET", path, null, params);
    }

    /**
     * 将应用迁移为工作流编排
     */
    public JSONObject convertToWorkflow(String appId, CreateAppDto dto) {
        String path = String.format("/apps/%s/convert-to-workflow", appId);
        JSONObject req = JSONObject.from(dto);
        if (dto.getIconType() != null) {
            req.put("icon_type", dto.getIconType());
        }
        if (dto.getIcon() != null) {
            req.put("icon", dto.getIcon());
        }
        if (dto.getIconBackground() != null) {
            req.put("icon_background", dto.getIconBackground());
        }
        return executeRequestWithConsole("POST", path, req, null);
    }

    /**
     * 获取标签列表
     */
    public JSONObject getTags(String type) {
        String path = "/tags";
        Map<String, Object> params = new HashMap<>();
        if (StringUtils.isNotBlank(type)) {
            params.put("type", type);
        }
        return executeRequestWithConsole("GET", path, null, params);
    }

    /**
     * 创建标签
     */
    public JSONObject createTag(String name, String type) {
        String path = "/tags";
        JSONObject req = new JSONObject();
        req.put("name", name);
        req.put("type", type);
        return executeRequestWithConsole("POST", path, req, null);
    }

    /**
     * 更新标签
     */
    public JSONObject updateTag(String tagId, JSONObject dto) {
        String path = String.format("/tags/%s", tagId);
        return executeRequestWithConsole("PATCH", path, dto, null);
    }

    /**
     * 删除标签
     */
    public JSONObject deleteTag(String tagId) {
        String path = String.format("/tags/%s", tagId);
        return executeRequestWithConsole("DELETE", path, null, null);
    }

    /**
     * 应用编排
     */
    public JSONObject updateModelConfig(String appId, ModelConfigDto dto) {
        String path = String.format("/apps/%s/model-config", appId);
        return executeRequestWithConsole("PUT", path, dto, null);
    }

    /**
     * 获取默认模型
     */
    public JSONObject getDefaultModel(String modelType) {
        String path = "/workspaces/current/default-model";
        Map<String, Object> params = new HashMap<>();
        params.put("model_type", modelType);
        return executeRequestWithConsole("GET", path, null, params);
    }

    /**
     * 生成提示词
     */
    public JSONObject ruleGenerate(RuleGenerateDto dto) {
        String path = "/rule-generate";
        return executeRequestWithConsole("POST", path, dto, null);
    }

    /**
     * 获取工具-类别
     */
    public JSONObject getToolLabels() {
        String path = "/workspaces/current/tool-labels";
        return executeRequestWithConsole("GET", path, null, null);
    }

    /**
     * 获取内置工具列表
     */
    public JSONObject getBuiltinTools() {
        String path = "/workspaces/current/tools/builtin";
        return executeRequestWithConsole("GET", path, null, null);
    }

    /**
     * 获取自定义工具列表
     */
    public JSONObject getCustomTools() {
        String path = "/workspaces/current/tools/api";
        return executeRequestWithConsole("GET", path, null, null);
    }


    /**
     * 获取工作流工具列表
     */
    public JSONObject getWorkflowTools() {
        String path = "/workspaces/current/tools/workflow";
        return executeRequestWithConsole("GET", path, null, null);
    }
}


