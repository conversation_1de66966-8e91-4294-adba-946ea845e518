package com.ruoyi.model_deploy.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.http.HttpUtils;
import com.ruoyi.model_deploy.utils.JsonUtils;
import com.ruoyi.model_deploy.domain.request.AddToolsRequest;
import com.ruoyi.model_deploy.domain.request.UpdateToolsRequest;
import com.ruoyi.model_deploy.domain.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.ruoyi.model_deploy.constant.ThirdModelsUrl.*;

@Service
@Slf4j
public class IMcpService {

    @Value("${knowledge.console-url}")
    private String consoleUrl;
    @Value("${knowledge.console-token}")
    private String consoleToken;


    public List<DifyMcpToolsVO> getMcpToolProviders() {
        JSONObject object = executeRequestWithConsole("GET", TOOLS_PROVIDERS_BASE_URL, null, null);
        JSONArray orgData = object.getJSONArray("data");
        List<DifyMcpToolsVO> difyMcpToolsVO = new ArrayList<>();
        for (int i = 0; i < orgData.size(); i++) {
            JSONObject json = orgData.getJSONObject(i);
            DifyMcpToolsVO tool = new DifyMcpToolsVO();
            String type = json.getString("type");
            if (!type.equals("api")) {
                continue;
            }
            tool.setId(json.getString("id"));
            tool.setAuthor(json.getString("author"));
            tool.setName(json.getString("name"));
            tool.setDescription(JsonUtils.getNestedString(json, null, "description", "zh_Hans"));
            tool.setIcon(json.getJSONObject("icon"));
            tool.setLabel(JsonUtils.getNestedString(json, null, "label", "zh_Hans"));
            tool.setType(type);
            tool.setTeam_credentials(json.getJSONObject("team_credentials"));
            tool.setIs_team_authorization(json.getBoolean("is_team_authorization"));
            tool.setAllow_delete(json.getBoolean("allow_delete"));
            tool.setTools(json.getJSONArray("tools"));
            tool.setLabels(json.getJSONArray("labels"));
            difyMcpToolsVO.add(tool);
        }
        return difyMcpToolsVO;
    }


    public ToolDetailVO getToolDetail(String provider) {
        JSONObject object = executeRequestWithConsole("GET", TOOLS_GET_BASE_URL + "?provider=" + provider, null, null);
        ToolDetailVO detailVO = new ToolDetailVO();
        detailVO.setCredentials(object.getJSONObject("credentials"));
        detailVO.setCustom_disclaimer(object.getString("custom_disclaimer"));
        detailVO.setDescription(object.getString("description"));
        detailVO.setIcon(object.getJSONObject("icon"));
        detailVO.setPrivacy_policy(object.getString("privacy_policy"));
        detailVO.setSchema(object.getString("schema"));
        detailVO.setSchema_type(object.getString("schema_type"));

        List<ToolVO> toolVOS = new ArrayList<>();
        object.getJSONArray("tools").forEach(item -> {
            JSONObject json = (JSONObject) item;
            ToolVO toolVO = new ToolVO();
            toolVO.setAuthor(json.getString("author"));
            toolVO.setIcon(json.getString("icon"));
            toolVO.setMethod(json.getString("method"));
            toolVO.setOpenapi(JSON.parseObject(json.getString("openapi"), OpenapiVO.class));
            toolVO.setOperation_id(json.getString("operation_id"));
            List<ParameterVO> parameterVOS = new ArrayList<>();
            JSONArray parameters = json.getJSONArray("parameters");
            parameters.forEach(item1 -> {
                JSONObject json1 = (JSONObject) item1;
                ParameterVO parameterVO = new ParameterVO();
                parameterVO.setDefaultValue(json1.getString("default"));
                parameterVO.setForm(json1.getString("form"));
                parameterVO.setHuman_description(JsonUtils.getNestedString(json, null, "human_description", "zh_Hans"));
                parameterVO.setLabel(JsonUtils.getNestedString(json, null, "label", "zh_Hans"));
                parameterVO.setLlm_description(json1.getString("llm_description"));
                parameterVO.setMax(json1.getString("max"));
                parameterVO.setMin(json1.getString("min"));
                parameterVO.setName(json1.getString("name"));
                parameterVO.setOptions(json1.getString("options"));
                parameterVO.setPlaceholder(JsonUtils.getNestedString(json1, null, "placeholder", "zh_Hans"));
                parameterVO.setRequired(json1.getBoolean("required"));
                parameterVO.setType(json1.getString("type"));
                parameterVOS.add(parameterVO);
            });
            toolVO.setServer_url(json.getString("server_url"));
            toolVO.setSummary(json.getString("summary"));
            toolVO.setParameters(parameterVOS);
            toolVOS.add(toolVO);
        });
        detailVO.setTools(toolVOS);
        return detailVO;
    }


    public R addTool(AddToolsRequest request) {
        log.info("编辑自定义工具:{}", JSONObject.toJSONString(request));
        JSONObject body = new JSONObject();
        body.put("schema_type", request.getSchema_type());
        body.put("schema", request.getSchema());
        body.put("icon", request.getIcon());
        body.put("description", request.getDescription());
        body.put("credentials", request.getCredentials());
        body.put("privacy_policy", StringUtils.isNotBlank(request.getPrivacy_policy()) ? request.getPrivacy_policy() : "");
        body.put("custom_disclaimer", request.getCustom_disclaimer());
        body.put("labels", request.getLabels());
        body.put("provider", request.getProvider());
        JSONObject resp = executeRequestWithConsole("POST", TOOLS_ADD_BASE_URL, body, null);
        if (resp.get("result").equals("success")) {
            return R.ok();
        }
        return R.fail();
    }

    public R updateTool(UpdateToolsRequest request) {
        log.info("编辑自定义工具:{}", JSONObject.toJSONString(request));
        JSONObject body = new JSONObject();
        body.put("schema_type", request.getSchema_type());
        body.put("schema", request.getSchema());
        body.put("icon", request.getIcon());
        body.put("description", request.getDescription());
        body.put("credentials", request.getCredentials());
        body.put("privacy_policy", StringUtils.isNotBlank(request.getPrivacy_policy()) ? request.getPrivacy_policy() : "");
        body.put("custom_disclaimer", request.getCustom_disclaimer());
        body.put("labels", request.getLabels());
        body.put("provider", request.getProvider());
        body.put("original_provider", request.getOriginal_provider());
        JSONObject resp = executeRequestWithConsole("POST", TOOLS_UPDATE_BASE_URL, body, null);
        if (resp.get("result").equals("success")) {
            return R.ok();
        }
        return R.fail();
    }

    public List<ToolLabelVO> getToolLabels(){
        JSONObject object = executeRequestWithConsole("GET", TOOLS_LABELS_URL , null, null);
        List<ToolLabelVO> toolLabelVOS=new ArrayList<>();
        object.getJSONArray("data").forEach(item -> {
            JSONObject json = (JSONObject) item;
            ToolLabelVO toolLabelVO = new ToolLabelVO();
            toolLabelVO.setIcon(json.getString("icon"));
            toolLabelVO.setName(json.getString("name"));
            toolLabelVO.setLabel(JsonUtils.getNestedString(json, null, "label", "zh_Hans"));
            toolLabelVOS.add(toolLabelVO);
        });
        return toolLabelVOS;
    }

    public R deleteTool(String provider) {
        log.info("删除自定义工具:{}", JSONObject.toJSONString(provider));
        JSONObject body = new JSONObject();
        body.put("provider", provider);
        JSONObject resp = executeRequestWithConsole("POST", TOOLS_DELETE_BASE_URL, body, null);
        if (resp.get("result").equals("success")) {
            return R.ok();
        }
        return R.fail();
    }

    private JSONObject executeRequestWithConsole(String method, String path, Object body, Map<String, Object> params) {
        return HttpUtils.executeAuthRequest(method, consoleUrl, path, body, params, consoleToken);
    }

}
