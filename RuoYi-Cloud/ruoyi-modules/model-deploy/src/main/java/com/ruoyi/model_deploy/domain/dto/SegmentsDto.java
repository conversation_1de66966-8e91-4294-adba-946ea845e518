package com.ruoyi.model_deploy.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "知识库分段信息DTO")
public class SegmentsDto {

    @Schema(description = "文本内容/问题内容", example = "这是一段知识库文本内容", required = true)
    private String content;

    @Schema(description = "答案内容（知识库为Q&A模式时必填）", example = "这是问题的标准答案")
    private String answer;

    @Schema(description = "关键字列表", example = "[\"AI\", \"机器学习\"]")
    private List<String> keywords;

    @Schema(description = "是否启用", example = "true", defaultValue = "true")
    private Boolean enabled;

    @Schema(description = "是否重新生成子分段", example = "false", defaultValue = "false")
    private Boolean regenerate_child_chunks;

}
