package com.ruoyi.model_deploy.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 微调任务对象 finetune_jobs
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FinetuneJobs extends BaseModelEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId
    private Integer finetuneJobId;

    /**
     * 名称
     */
    @Excel(name = "名称")
    private String name;

    /**
     * 任务唯一标识符
     */
    @Excel(name = "任务唯一标识符")
    private String jobId;

    /**
     * 微调服务器ID
     */
    @Excel(name = "微调服务器ID")
    private Integer finetuneServerId;

    /**
     * 基础模型ID
     */
    @Excel(name = "基础模型ID")
    private String baseModelId;

    /**
     * 数据集ID
     */
    @Excel(name = "数据集ID")
    private String datasetId;

    /**
     * 训练参数(学习率、epoch等)
     */
    @Excel(name = "训练参数")
    private String hyperparameters;

    /**
     * 任务状态
     */
    @Excel(name = "任务状态", readConverterExp = "pending=等待中,running=运行中,success=成功,failed=失败")
    private JobStatus status;

    /**
     * 微调结果磁盘路径
     */
    @Excel(name = "微调结果磁盘路径")
    private String outputPath;

    /**
     * 产出模型ID
     */
    @Excel(name = "产出模型ID")
    private String outputModelId;

    /**
     * 训练日志存储路径
     */
    @Excel(name = "训练日志存储路径")
    private String logPath;

    /**
     * 任务开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 任务结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 任务状态枚举
     */
    public enum JobStatus {
        init,
        pending,
        running,
        success,
        running_failed,
        dataset_init_failed,
        model_init_failed
    }

    @TableField(exist = false)
    private String customBaseModelName;

    @TableField(exist = false)
    private List<JobStatus> statusList;
}