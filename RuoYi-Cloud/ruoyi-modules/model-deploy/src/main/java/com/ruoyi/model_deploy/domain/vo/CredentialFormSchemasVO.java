package com.ruoyi.model_deploy.domain.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "添加模型时的表单参数")
public class CredentialFormSchemasVO {

    @Schema(description = "表单默认值")
    private String defaultValue;

    @Schema(description = "添加模型时的表单头")
    private String label;

    @Schema(description = "")
    private String maxLength;

    @Schema(description = "")
    private String options;

    @Schema(description = "提示词")
    private String placeholder;

    @Schema(description = "是否必填")
    private String required;

    @Schema(description = "输入框类型")
    private String type;

    @Schema(description = "表单提交参数")
    private String variable;
}
