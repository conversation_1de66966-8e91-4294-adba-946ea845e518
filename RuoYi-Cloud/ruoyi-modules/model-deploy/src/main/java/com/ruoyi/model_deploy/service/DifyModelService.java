package com.ruoyi.model_deploy.service;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.utils.http.HttpUtils;
import com.ruoyi.model_deploy.domain.ComputeResources;
import com.ruoyi.model_deploy.enums.DifyGpuStackModelTypeEnum;
import com.ruoyi.model_deploy.mapper.ComputeResourcesMapper;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Map;

/**
 * dify模型相关接口
 * 复用 KnowledgeService的代码
 */
@Service
@Slf4j
public class DifyModelService {
    @Value("${knowledge.api-url}")
    private String baseUrl;
    @Value("${knowledge.api-key}")
    private String apiKey;
    @Value("${knowledge.console-url}")
    private String consoleUrl;
    @Value("${knowledge.console-token}")
    private String consoleToken;

    @Resource
    private ComputeResourcesMapper computeResourcesMapper;


    /**
     * dify 接口格式如下：
     *
     * {
     *     "model": "vvvv",
     *     "model_type": "text-embedding",
     *     "credentials": {
     *         "context_size": "8192",
     *         "endpoint_url": "http://192.168.30.33:9999",
     *         "api_key": "gpustacklasjkdf"
     *     },
     *     "load_balancing": {
     *         "enabled": false,
     *         "configs": []
     *     }
     * }
     */

    public void addDifyModel(DifyModelContext modelContext) {

        log.info("添加模型:{},模型类型: {}到dify平台",modelContext.modelName,modelContext.modelType);
        //1.准备数据
        ComputeResources computeResources =getGpustackComputeResources();
        String endpointUrl = "http://" + computeResources.getIp() + ":" + computeResources.getGpustackPort();
        String apiKey = computeResources.getGpustackToken();
        String contextSize = "8192";

        JSONObject body = new JSONObject();
        body.put("model", modelContext.modelName);
        body.put("model_type", modelContext.modelType);

        JSONObject credentials = new JSONObject();
        credentials.put("context_size", contextSize);
        credentials.put("endpoint_url", endpointUrl);
        credentials.put("api_key", apiKey);

        if (DifyGpuStackModelTypeEnum.LLM.getDifyModelTypeName().equals(modelContext.modelType)) {
            String maxTokensToSample = "8192";
            credentials.put("max_tokens_to_sample", maxTokensToSample);
        }


        body.put("credentials", credentials);

        JSONObject loadBalance = new JSONObject();
        loadBalance.put("enabled", false);
        loadBalance.put("configs", new ArrayList<>());
        body.put("load_balancing", loadBalance);

        //2. 调用接口添加模型
        String path ="/workspaces/current/model-providers/gpustack/models";
        executeRequestWithConsole("POST",path,body,null);
    }

    /**
     * 请求格式
     * {
     *     "model": "deepseek-r1:32b",
     *     "model_type": "llm"
     * }
     */
    public void deleteDifyModel(DifyModelContext difyModelContext){
        log.info("删除dify平台模型:{},模型类型: {}",difyModelContext.modelName,difyModelContext.modelType);
        JSONObject body = new JSONObject();
        body.put("model", difyModelContext.modelName);
        body.put("model_type", difyModelContext.modelType);
        String path ="/workspaces/current/model-providers/gpustack/models";
        executeRequestWithConsole("DELETE",path,body,null);
    }

    /**
     * 查询gpustack信息
     */
    private ComputeResources  getGpustackComputeResources(){
        return computeResourcesMapper.selectOne(
                Wrappers.<ComputeResources>lambdaQuery()
                        .eq(ComputeResources::getIsMaster, 1)
                        .eq(ComputeResources::getType, ComputeResources.ResourceType.inference));
    }

    /**
     *dify模型信息
     */
    @Builder
    public static class DifyModelContext{
         String modelName;
         String modelType;
    }

    /**
     * 执行HTTP请求的通用方法
     *
     * @param method HTTP方法
     * @param path   请求路径
     * @param body   请求体
     * @param params 查询参数
     * @return 响应结果
     */
    private JSONObject executeRequestWithApiKey(String method, String path, Object body, Map<String, Object> params) {
        return HttpUtils.executeAuthRequest(method, baseUrl, path, body, params, apiKey);
    }

    private JSONObject executeRequestWithConsole(String method, String path, Object body, Map<String, Object> params) {
        return HttpUtils.executeAuthRequest(method, consoleUrl, path, body, params, consoleToken);
    }
}
