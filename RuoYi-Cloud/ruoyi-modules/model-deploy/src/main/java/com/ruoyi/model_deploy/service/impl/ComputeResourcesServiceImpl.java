package com.ruoyi.model_deploy.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.model_deploy.domain.ComputeResources;
import com.ruoyi.model_deploy.mapper.ComputeResourcesMapper;
import com.ruoyi.model_deploy.service.IComputeResourcesService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 算力资源Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class ComputeResourcesServiceImpl extends ServiceImpl<ComputeResourcesMapper, ComputeResources> implements IComputeResourcesService {
    @Resource
    private ComputeResourcesMapper computeResourcesMapper;

    /**
     * 查询算力资源
     *
     * @param serverId 算力资源主键
     * @return 算力资源
     */
    @Override
    public ComputeResources selectComputeResourcesById(Long serverId) {
        return computeResourcesMapper.selectComputeResourcesById(serverId);
    }

    /**
     * 查询算力资源列表
     *
     * @param computeResources 算力资源
     * @return 算力资源
     */
    @Override
    public IPage<ComputeResources> selectComputeResourcesList(ComputeResources computeResources) {
        return computeResourcesMapper.selectPage(new Page<>(computeResources.getPageNum(), computeResources.getPageSize()),
                Wrappers.<ComputeResources>lambdaQuery()
                        .eq(null != computeResources.getType(), ComputeResources::getType, computeResources.getType()));
    }

    /**
     * 新增算力资源
     *
     * @param computeResources 算力资源
     * @return 结果
     */
    @Override
    public int insertComputeResources(ComputeResources computeResources) {
        return computeResourcesMapper.insertComputeResources(computeResources);
    }

    /**
     * 修改算力资源
     *
     * @param computeResources 算力资源
     * @return 结果
     */
    @Override
    public int updateComputeResources(ComputeResources computeResources) {
        return computeResourcesMapper.updateComputeResources(computeResources);
    }

    /**
     * 批量删除算力资源
     *
     * @param serverIds 需要删除的算力资源主键
     * @return 结果
     */
    @Override
    public int deleteComputeResourcesByIds(Long[] serverIds) {
        return computeResourcesMapper.deleteComputeResourcesByIds(serverIds);
    }

    /**
     * 删除算力资源信息
     *
     * @param serverId 算力资源主键
     * @return 结果
     */
    @Override
    public int deleteComputeResourcesById(Long serverId) {
        return computeResourcesMapper.deleteComputeResourcesById(serverId);
    }
}