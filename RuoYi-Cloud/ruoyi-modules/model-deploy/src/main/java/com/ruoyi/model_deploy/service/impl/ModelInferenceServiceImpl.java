package com.ruoyi.model_deploy.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.model_deploy.domain.ComputeResources;
import com.ruoyi.model_deploy.domain.ModelInference;
import com.ruoyi.model_deploy.domain.ModelRegistry;
import com.ruoyi.model_deploy.enums.DifyGpuStackModelTypeEnum;
import com.ruoyi.model_deploy.mapper.ComputeResourcesMapper;
import com.ruoyi.model_deploy.mapper.ModelInferenceMapper;
import com.ruoyi.model_deploy.mapper.ModelRegistryMapper;
import com.ruoyi.model_deploy.service.DifyModelService;
import com.ruoyi.model_deploy.service.IModelInferenceService;
import com.ruoyi.model_deploy.service.ModelSquareService;
import com.ruoyi.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 模型推理Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ModelInferenceServiceImpl extends ServiceImpl<ModelInferenceMapper, ModelInference> implements IModelInferenceService {

    @Resource
    private ModelInferenceMapper modelInferenceMapper;
    @Resource
    private ModelRegistryMapper modelRegistryMapper;
    @Resource
    private ComputeResourcesMapper computeResourcesMapper;
    @Resource
    private ModelSquareService modelSquareService;
    @Resource
    DifyModelService difyModelService;

    /**
     * 查询模型推理
     *
     * @param inferenceId 模型推理主键
     * @return 模型推理
     */
    @Override
    public ModelInference selectModelInferenceById(Long inferenceId) {
        return modelInferenceMapper.selectModelInferenceById(inferenceId);
    }

    /**
     * 查询模型推理列表
     *
     * @param modelInference 模型推理
     * @return 模型推理
     */
    @Override
    public IPage<ModelInference> selectModelInferenceList(ModelInference modelInference) {
        IPage<ModelInference> page = modelInferenceMapper.selectPage(new Page<>(modelInference.getPageNum(), modelInference.getPageSize()), Wrappers.<ModelInference>lambdaQuery()
                .eq(null != modelInference.getStatus(), ModelInference::getStatus, modelInference.getStatus())
                .eq(modelInference.getModelType() != null, ModelInference::getModelType, modelInference.getModelType())
                .eq(ModelInference::getCreatedBy, Math.toIntExact(SecurityUtils.getUserId()))
        );
        List<ModelInference> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return page;
        }
        //baseModelId->ModelRegistry
        Map<Integer, ModelRegistry> modelRegistryMap = modelRegistryMapper.selectList(Wrappers.<ModelRegistry>lambdaQuery()
                        .in(ModelRegistry::getModelRegistryId, records.stream().map(ModelInference::getModelRegistryId).collect(Collectors.toSet())))
                .stream().collect(Collectors.toMap(ModelRegistry::getModelRegistryId, m -> m));
        JSONObject request = new JSONObject();
        request.put("page", 1);
        request.put("perPage", 100);
        records.forEach(item->{
            ModelRegistry modelRegistry = modelRegistryMap.get(item.getModelRegistryId());
            if (null != modelRegistry) {
                item.setModelRegistryName(modelRegistry.getCustomModelName());
                item.setModelRegistrySource(modelRegistry.getModelSource());
                item.setModelRegistryPath(modelRegistry.getStoragePath());
                item.setModelRegistryGguf(modelRegistry.getIsGguf());
            }
            if (null != item.getGpustackId()) {
                item.setInstances(modelSquareService.inferenceApi("/v1/models/" + item.getGpustackId() + "/instances", request, "GET", true, false));
            }
        });
        page.setRecords(records);
        return page;
    }

    /**
     * 新增模型推理
     *
     * @param modelInference 模型推理
     * @return 结果
     */
    @Override
    public int insertModelInference(ModelInference modelInference) {
        return modelInferenceMapper.insertModelInference(modelInference);
    }

    /**
     * 修改模型推理
     *
     * @param modelInference 模型推理
     * @return 结果
     */
    @Override
    public int updateModelInference(ModelInference modelInference) {
        return modelInferenceMapper.updateModelInference(modelInference);
    }

    /**
     * 批量删除模型推理
     *
     * @param inferenceIds 需要删除的模型推理主键
     * @return 结果
     */
    @Override
    public int deleteModelInferenceByIds(Long[] inferenceIds) {
        return modelInferenceMapper.deleteModelInferenceByIds(inferenceIds);
    }

    /**
     * 删除模型推理
     */
    @Override
    public void deleteModelInferenceById(Integer inferenceId) {
        ModelInference modelInference = modelInferenceMapper.selectById(inferenceId);
        if (null == modelInference) {
            throw new RuntimeException("推理任务不存在");
        }
        //删除gpustack
        if (null != modelInference.getGpustackId()) {
            // 需要删除dify,这里暂时没有建表存储 dify的模型部署信息， 需要从gpustack查询一遍 模型信息
            String url = "/v1/models/".concat(modelInference.getGpustackId().toString());
            JSONObject responseObject = modelSquareService.inferenceApi(url, null, "GET", true, false);
            delegeDifyModel(responseObject);
            //删除gpustack
            modelSquareService.inferenceApi("/v1/models/" + modelInference.getGpustackId(), null, "DELETE", true, false);
        }
        modelInferenceMapper.deleteById(inferenceId);
    }

    /**
     * 删除模型推理部署
     */
    @Override
    public void deleteModelInferenceDeploy(Integer inferenceId) {
        ModelInference modelInference = modelInferenceMapper.selectById(inferenceId);
        if (null == modelInference) {
            throw new RuntimeException("推理任务不存在");
        }
        if (null != modelInference.getGpustackId()) {
            // 需要删除dify,这里暂时没有建表存储 dify的模型部署信息， 需要从gpustack查询一遍 模型信息
            String url = "/v1/models/".concat(modelInference.getGpustackId().toString());
            JSONObject responseObject = modelSquareService.inferenceApi(url, null, "GET", true, false);
            delegeDifyModel(responseObject);
            modelSquareService.inferenceApi("/v1/models/" + modelInference.getGpustackId(), null, "DELETE", true, false);
        }
        modelInferenceMapper.update(null, Wrappers.<ModelInference>lambdaUpdate()
                .eq(ModelInference::getModelInferenceId, inferenceId)
                .set(ModelInference::getStatus, ModelInference.InferenceStatus.init)
                .set(ModelInference::getGpustackId, null)
                .set(ModelInference::getReplicas, 0)
                .set(ModelInference::getDeployAt, new Date()));
    }

    /**
     * 模型推理初始化
     */
    @Override
    public void init(Integer modelId) {
        ModelRegistry modelRegistry = modelRegistryMapper.selectById(modelId);
        if (null == modelRegistry) {
            throw new RuntimeException("模型不存在");
        }
        ModelInference modelInference = modelInferenceMapper.selectOne(Wrappers.<ModelInference>lambdaQuery()
                .eq(ModelInference::getModelRegistryId, modelId)
                .ne(ModelInference::getStatus, ModelInference.InferenceStatus.init_failed));
        if (null != modelInference) {
            throw new RuntimeException("该模型已存在推理任务");
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long userid = loginUser.getUserid();
        modelInference = new ModelInference(){{
            setModelRegistryId(modelId);
            setStatus(InferenceStatus.init);
            setCreatedBy(Math.toIntExact(userid));
            setCreatedAt(new Date());
        }};
        modelInferenceMapper.insert(modelInference);
        //调用python模型推理初始化
        JSONObject requestBody = new JSONObject();
        requestBody.put("model_inference_id", modelInference.getModelInferenceId());
        ComputeResources computeResource = computeResourcesMapper.selectOne(Wrappers.<ComputeResources>lambdaQuery()
                .eq(ComputeResources::getIsMaster, 1)
                .eq(ComputeResources::getType, ComputeResources.ResourceType.inference));
        log.info("微调推理初始化请求");
        modelSquareService.computerApi("GET", computeResource, "/init_inference", requestBody, true);
    }

    @Override
    public JSONObject instances(Integer id) {
        ModelInference modelInference = modelInferenceMapper.selectById(id);
        if (null == modelInference) {
            throw new RuntimeException("推理任务不存在");
        }
        JSONObject request = new JSONObject();
        request.put("page", 1);
        request.put("perPage", 100);
        return modelSquareService.inferenceApi("/v1/models/" + modelInference.getGpustackId() + "/instances", request, "GET", true, false);
    }

    /**
     * 删除模型推理部署实例信息
     */
    @Override
    public void delInstance(Integer id) {
        modelSquareService.inferenceApi("/v1/model-instances/" + id, null, "DELETE", true, false);
    }

    /**
     * 模型推理-部署
     */
    @Override
    public void deploy(JSONObject dto) {
        Integer modelInferenceId = dto.getInteger("name");
        ModelInference modelInference = modelInferenceMapper.selectById(modelInferenceId);
        if (null == modelInference) {
            throw new RuntimeException("推理任务不存在");
        }
        if (!modelInference.getStatus().equals(ModelInference.InferenceStatus.inited) || StringUtils.isBlank(modelInference.getStoragePath())) {
            throw new RuntimeException("模型未初始化结束，不能部署");
        }
        if (null == modelInference.getGpustackId()) {
            String url = "/v1/models";
            log.info("开始模型推理-部署");
            JSONObject responseObject = modelSquareService.inferenceApi(url, dto, "POST", true, false);
            Integer gpustackId = responseObject.getInteger("id");
            // 异步添加dify模型
            CompletableFuture.runAsync(()->addDifyModel(gpustackId));
            //更新GPUStackId
            modelInferenceMapper.update(null, Wrappers.<ModelInference>lambdaUpdate()
                    .eq(ModelInference::getModelInferenceId, modelInferenceId)
                    .set(ModelInference::getGpustackId, gpustackId)
                    .set(ModelInference::getReplicas, dto.getString("replicas"))
                    .set(ModelInference::getModelType, dto.getJSONArray("categories").get(0))
                    .set(ModelInference::getDeployAt, new Date()));
        } else {
            log.info("开始模型推理-启动");
            this.updateReplicas(modelInferenceId, 1);
        }
    }

    /**
     * 删除dify中的模型
     */
    private void delegeDifyModel(JSONObject gpuStackResponseObject) {
        DifyModelService.DifyModelContext difyModelContext = getDifyModelContext(gpuStackResponseObject);
        if(difyModelContext == null) {
           log.info("dify模型删除失败，对应gpustack模型信息:{}", gpuStackResponseObject);
           return;
        }
        difyModelService.deleteDifyModel(difyModelContext);
    }

    /**
     * 从gpustack的模型信息中 读取模型名称和模型类型
     */
    private DifyModelService.DifyModelContext getDifyModelContext(JSONObject gpuStackModel) {
        String modelName = gpuStackModel.getString("name");
        JSONArray categories = gpuStackModel.getJSONArray("categories");
        if (categories.isEmpty()) {
            log.info("gpustack 模型:{} 缺少类型信息，",modelName);
            return null;
        }
        //默认使用LLM类型
        String difyCategory = DifyGpuStackModelTypeEnum.LLM.getDifyModelTypeName();
        for (int i = 0; i < categories.size(); i++) {
            String gpuStackCategory = categories.getString(i);
            String temp =  DifyGpuStackModelTypeEnum.getDifyModelTypeName(gpuStackCategory);
            if (temp != null) {
                difyCategory = temp;
                break;
            }
        }
        return DifyModelService.DifyModelContext.builder().modelType(difyCategory)
                .modelName(modelName).build();
    }

    /**
     */
    private void addDifyModel(Integer gpustackId) {
        int count = 0;
        JSONObject gpuStackResponseObject = null;
        //等待模型部署起来 测试时 5s内基本就能部署成功
        while (count++ < 10) {
            try {
                Thread.sleep(5000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            String tempUrl = "/v1/models/".concat(gpustackId.toString());
            gpuStackResponseObject = modelSquareService.inferenceApi(tempUrl, null, "GET", true, false);
            Integer readyReplicas = gpuStackResponseObject.getInteger("ready_replicas");
            if (readyReplicas != null && readyReplicas > 0) {
                break;
            }
        }
        DifyModelService.DifyModelContext difyModelContext = getDifyModelContext(gpuStackResponseObject);
        if (difyModelContext == null) {
            log.error("dify模型添加失败，对应gpustack模型信息:{}", gpuStackResponseObject);
            return;
        }
        difyModelService.addDifyModel(difyModelContext);
    }

    /**
     * 模型推理-副本数变化
     * 0:停止 1:启动 其他:更新副本数量
     */
    @Override
    public void updateReplicas(Integer id, Integer replicas) {
        if (replicas < 0) {
            throw new RuntimeException("副本数不能小于0");
        }
        ModelInference modelInference = modelInferenceMapper.selectById(id);
        if (null == modelInference) {
            throw new RuntimeException("推理任务不存在");
        }
        String url = "/v1/models/".concat(modelInference.getGpustackId().toString());
        log.info("获取详情");
        JSONObject responseObject = modelSquareService.inferenceApi(url, null, "GET", true, false);
        responseObject.put("replicas", replicas);
        log.info("副本数变化：{}", replicas);
        modelSquareService.inferenceApi(url, responseObject, "PUT", true, false);
        //更新数据库
        modelInferenceMapper.update(null, Wrappers.<ModelInference>lambdaUpdate()
                .eq(ModelInference::getModelInferenceId, id)
                .set(ModelInference::getReplicas, replicas)
                .set(ModelInference::getDeployAt, new Date()));
    }

    /**
     * GPUStack - 仪表盘
     */
    @Override
    public JSONObject dashboard() {
        String url = "/v1/dashboard";
        log.info("获取GPUStack-dashboard");
        JSONObject responseObject = modelSquareService.inferenceApi(url, null, "GET", true, false);
        return responseObject;
    }
}