package com.ruoyi.model_deploy.service.impl;

import com.ruoyi.model_deploy.domain.McpServerTool;
import com.ruoyi.model_deploy.domain.request.OptMcpToolRequest;
import com.ruoyi.model_deploy.domain.request.QueryMcpToolRequest;
import com.ruoyi.model_deploy.domain.vo.McpToolVO;
import com.ruoyi.model_deploy.mapper.McpServerToolMapper;
import com.ruoyi.model_deploy.service.IMcpServerToolService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * MCP服务工具Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class McpServerToolServiceImpl implements IMcpServerToolService {
    @Resource
    private McpServerToolMapper mcpServerToolMapper;

    @Value("${mcp.server-ip}")
    private String mcpServer;

    /**
     * 查询MCP服务工具
     *
     * @param id MCP服务工具主键
     * @return MCP服务工具
     */
    @Override
    public McpToolVO selectMcpServerToolById(Long id) {
        return mcpServerToolMapper.selectMcpServerToolById(id);
    }

    /**
     * 查询MCP服务工具列表
     *
     * @param mcpServerTool MCP服务工具
     * @return MCP服务工具
     */
    @Override
    public List<McpServerTool> selectMcpServerToolList(QueryMcpToolRequest mcpServerTool) {
        return mcpServerToolMapper.selectMcpServerToolList(mcpServerTool);
    }

    /**
     * 新增MCP服务工具
     *
     * @param request MCP服务工具
     * @return 结果
     */
    @Override
    public int insertMcpServerTool(OptMcpToolRequest request) {
        return mcpServerToolMapper.insertMcpServerTool(request);
    }

    /**
     * 修改MCP服务工具
     *
     * @param request MCP服务工具
     * @return 结果
     */
    @Override
    public int updateMcpServerTool(OptMcpToolRequest request) {
        return mcpServerToolMapper.updateMcpServerTool(request);
    }

    /**
     * 批量删除MCP服务工具
     *
     * @param ids 需要删除的MCP服务工具主键
     * @return 结果
     */
    @Override
    public int deleteMcpServerToolByIds(Long[] ids) {
        return mcpServerToolMapper.deleteMcpServerToolByIds(ids);
    }

    /**
     * 删除MCP服务工具信息
     *
     * @param id MCP服务工具主键
     * @return 结果
     */
    @Override
    public int deleteMcpServerToolById(Long id) {
        return mcpServerToolMapper.deleteMcpServerToolById(id);
    }
}