package com.ruoyi.model_deploy.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.model_deploy.domain.dto.ApiKeysDto;
import com.ruoyi.model_deploy.service.AppsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 应用管理Controller
 *
 * <AUTHOR>
 */
@Tag(name = "应用管理", description = "应用管理相关接口")
@RestController
@Slf4j
@RequestMapping("/apps")
public class AppsController extends BaseController {

    @Resource
    private AppsService appsService;


    @Operation(summary = "根据应用ID获取apikey", description = "根据应用ID获取apikey")
    @GetMapping("/getApikeysById/{appId}")
    public R getApikeysById(@Parameter(description = "应用ID") @PathVariable String appId) {

        return R.ok(appsService.getApikeysById(appId));
    }

    @Operation(summary = "根据应用ID创建apikey", description = "根据应用ID创建apikey")
    @PostMapping("/createApikeysById/{appId}")
    public R createApikeysById(@Parameter(description = "应用ID") @PathVariable String appId,
                               @Parameter(description = "API信息") @RequestBody ApiKeysDto apiKeysDto) throws Exception {

        return R.ok(appsService.createApikeysById(appId, apiKeysDto));
    }

    @Operation(summary = "根据ID删除apikey", description = "根据ID删除apikey")
    @GetMapping("/deleteApikeysById/{appId}/{apiKeyId}")
    public R deleteApikeysById(@Parameter(description = "应用ID") @PathVariable String appId,
                               @Parameter(description = "apikeyID") @PathVariable String apiKeyId) {

        return R.ok(appsService.deleteApikeysById(appId, apiKeyId));
    }


    @Operation(summary = "根据ID获取会话日志", description = "根据ID获取会话日志")
    @GetMapping("/getConversationsLogs/{appId}")
    public R getConversationsLogs(@Parameter(description = "应用ID") @PathVariable String appId,
                                  @Parameter(description = "页码") @RequestParam Integer page,
                                  @Parameter(description = "页码条目数") @RequestParam Integer limit,
                                  @Parameter(description = "开始时间") @RequestParam String start,
                                  @Parameter(description = "结束时间") @RequestParam String end,
                                  @Parameter(description = "排序") @RequestParam String sortBy) {

        return R.ok(appsService.getConversationsLogs(appId, page, limit, start, end, sortBy));
    }

    @Operation(summary = "根据ID获取日志基础信息", description = "根据ID获取日志基础信息")
    @GetMapping("/getConversationsLogsForBaseInfo/{appId}/{conversationId}")
    public R getConversationsLogsForBaseInfo(@Parameter(description = "应用ID") @PathVariable String appId,
                                             @Parameter(description = "会话ID") @PathVariable String conversationId) {

        return R.ok(appsService.getConversationsLogsForBaseInfo(appId, conversationId));
    }

    @Operation(summary = "根据ID获取日志详细信息", description = "根据ID获取日志详细信息")
    @GetMapping("/getConversationsLogsForDetailInfo/{appId}/{conversationId}")
    public R getConversationsLogsForDetailInfo(@Parameter(description = "应用ID") @PathVariable String appId,
                                               @Parameter(description = "会话ID") @PathVariable String conversationId,
                                               @Parameter(description = "页码条目数") @RequestParam Integer limit) {

        return R.ok(appsService.getConversationsLogsForDetailInfo(appId, conversationId, limit));
    }


    @Operation(summary = "根据ID获取工作流会话", description = "根据ID获取工作流会话")
    @GetMapping("/getWorkflowAppLogs/{appId}")
    public R getWorkflowAppLogs(@Parameter(description = "应用ID") @PathVariable String appId,
                                @Parameter(description = "页码") @RequestParam Integer page,
                                @Parameter(description = "页码条目数") @RequestParam Integer limit,
                                @Parameter(description = "状态") @RequestParam String status,
                                @Parameter(description = "关键词") @RequestParam String keyword) {

        return R.ok(appsService.getWorkflowAppLogs(appId, page, limit, status, keyword));
    }

    @Operation(summary = "根据ID获取工作流日志详细信息", description = "根据ID获取工作流日志详细信息")
    @GetMapping("/getWorkflowRuns/{appId}/{workflowId}")
    public R getWorkflowRuns(@Parameter(description = "应用ID") @PathVariable String appId,
                             @Parameter(description = "会话ID") @PathVariable String workflowId
    ) {

        return R.ok(appsService.getWorkflowRuns(appId, workflowId));
    }


    @Operation(summary = "根据ID获取工作流执行情况", description = "根据ID获取工作流执行情况")
    @GetMapping("/getWorkflowRunsForNodeExecutions/{appId}/{workflowId}")
    public R getWorkflowRunsForNodeExecutions(@Parameter(description = "应用ID") @PathVariable String appId,
                                              @Parameter(description = "会话ID") @PathVariable String workflowId
    ) {

        return R.ok(appsService.getWorkflowRunsForNodeExecutions(appId, workflowId));
    }
}
