package com.ruoyi.model_deploy.controller;


import com.ruoyi.common.core.domain.R;
import com.ruoyi.model_deploy.domain.request.AccessModelReq;
import com.ruoyi.model_deploy.service.IThirdModelsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 第三方模型接入
 */
@RestController
@RequestMapping("/thirdModels")
public class ThirdModelsController {


    @Resource
    private IThirdModelsService iThirdModelsService;

    @Operation(summary = "查询模型", description = "查询第三方模型供应商")
    @GetMapping("/providers")
    public R getThirdProviders() {
        return R.ok(iThirdModelsService.getProviders());
    }


    @Operation(summary = "子模型列表", description = "查询本地接入的第三方模型的子模型列表")
    @GetMapping("/child-models/{provider}")
    public R getChildModelsList(@Parameter(description = "供应商") @PathVariable("provider") String provider) {
        return R.ok(iThirdModelsService.getChildModels(provider));
    }

    @Operation(summary = "模型凭证", description = "查询本地接入模型的凭证")
    @GetMapping("/credentials/{provider}")
    public R getCredentials(@Parameter(description = "供应商") @PathVariable("provider") String provider) {
        return R.ok(iThirdModelsService.getCredentials(provider));
    }

    @Operation(summary = "接入模型", description = "本地接入第三方模型操作")
    @PostMapping("/access")
    public R addModels(@Parameter(description = "接入模型参数") @RequestBody AccessModelReq req) {
        return iThirdModelsService.accessModel(req);
    }

    @Operation(summary = "移除模型", description = "移除本地接入第三方模型操作")
    @DeleteMapping("/remove/{provider}")
    public R delModels(@Parameter(description = "供应商") @PathVariable("provider") String provider) {
        iThirdModelsService.delModels(provider);
        return R.ok();
    }


}
