package com.ruoyi.model_deploy.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 模型注册表对象 model_registry
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ModelRegistry extends BaseModelEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Integer modelRegistryId;

    /**
     * 全局唯一标识符
     */
    @Excel(name = "全局唯一标识符")
    private String modelscopeModelId;

    /**
     * 模型名称
     */
    @Excel(name = "模型名称")
    private String modelName;

    /**
     * 自定义模型名称
     */
    @Excel(name = "自定义模型名称")
    private String customModelName;

    /**
     * 来源(huggingface/modelscope/custom)
     */
    @Excel(name = "模型来源", readConverterExp = "huggingface=HuggingFace,modelscope=ModelScope,custom=自定义")
    private ModelSource modelSource;

    /**
     * 是否为gguf格式的模型，0为否，1为是，默认0
     */
    @Excel(name = "是否为gguf格式的模型", readConverterExp = "0=否,1=是")
    private Boolean isGguf;

    /**
     * 基础模型ID
     */
    @Excel(name = "基础模型ID")
    private String baseModel;

    /**
     * 语义化版本号
     */
    @Excel(name = "版本号")
    private String version;

    /**
     * 模型文件存储路径
     */
    @Excel(name = "存储路径")
    private String storagePath;

    /**
     * 当前状态
     */
    @Excel(name = "当前状态", readConverterExp = "init/downloading/downloaded/uploading/uploaded")
    private ModelStatus status;

    /**
     * 是否公开
     */
    @Excel(name = "是否公开", readConverterExp = "0=否,1=是")
    @TableField("public")
    private Boolean isPublic;

    /**
     * 附加元数据
     */
    @Excel(name = "附加元数据")
    private String metadata;

    /**
     * 模型描述
     */
    @Excel(name = "模型描述")
    private String description;

    /**
     * 模型来源枚举
     */
    public enum ModelSource {
        huggingface,
        modelscope,
        custom
    }

    /**
     * 模型状态枚举
     */
    public enum ModelStatus {
        init,
        downloading,
        downloaded,
        uploading,
        uploaded,
        download_failed,
        upload_failed
    }
}