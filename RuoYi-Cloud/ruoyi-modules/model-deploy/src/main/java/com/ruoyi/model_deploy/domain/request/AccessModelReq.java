package com.ruoyi.model_deploy.domain.request;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "添加模型时的表单参数")
public class AccessModelReq {


    @Schema(description = "提交表单类型")
    private String config_from;


    @Schema(description = "表单内部参数",example = "    \"credentials\": {\n" +
            "        \"openai_api_key\": \"sk-Fu5kdvaQyw8Xr5rTha9vhCcFZmfefElK5EU7itujSpELjUWo\",\n" +
            "        \"openai_api_base\": \"https://api.chatanywhere.tech\"\n" +
            "    }")
    private JSONObject credentials;

    @Schema(description = "供应商")
    private String provider;


    @Schema(description = "模型类型")
    private String model_type;

    @Schema(description = "模型名称")
    private String model;

}
