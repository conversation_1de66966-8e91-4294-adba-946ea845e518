package com.ruoyi.model_deploy.service;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.utils.http.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 知识库文档
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AppsService {

    @Value("${knowledge.api-url}")
    private String baseUrl;
    @Value("${knowledge.api-key}")
    private String apiKey;
    @Value("${knowledge.console-url}")
    private String consoleUrl;
    @Value("${knowledge.console-token}")
    private String consoleToken;


    /**
     * 根据应用ID获取apikey
     *
     * @param appId
     * @return
     */
    public JSONObject getApikeysById(String appId) {
        String path = String.format("/apps/%s/api-keys", appId);
        return executeRequestWithConsole("GET", path, new JSONObject(), null);
    }


    /**
     * 根据ID创建apikey
     *
     * @param appId
     * @return
     * @throws Exception
     */
    public JSONObject createApikeysById(String appId)  {
        String path = String.format("/apps/%s/api-keys", appId);
        return executeRequestWithConsole("POST", path, null, null);

    }

    /**
     * 根据ID删除apikey
     *
     * @param appId
     * @param apiKeyId
     * @return
     */
    public JSONObject deleteApikeysById(String appId, String apiKeyId) {
        String path = String.format("/apps/%s/api-keys/%s", appId, apiKeyId);
        return executeRequestWithConsole("DELETE", path, new JSONObject(), null);
    }


    /**
     * 根据ID获取会话日志
     *
     * @param appId
     * @param page
     * @param limit
     * @param start
     * @param end
     * @param sortBy
     * @return
     */
    public JSONObject getConversationsLogs(String appId, Integer page, Integer limit, String start, String end, String sortBy) {
        String path = String.format("/apps/%s/chat-conversations", appId);
        Map<String, Object> params = new HashMap<>();
        params.put("page", page);
        params.put("limit", limit);
        params.put("sort_by", sortBy);
        params.put("annotation_status", "all");
        if (Strings.isNotBlank(start)) {
            params.put("start", start);
        }
        if (Strings.isNotBlank(end)) {
            params.put("end", end);
        }
        return executeRequestWithConsole("GET", path, null, params);
    }


    /**
     * 根据ID获取日志基础信息
     * @param appId
     * @param conversationId
     * @return
     */
    public JSONObject getConversationsLogsForBaseInfo(String appId, String conversationId) {
        String path = String.format("/apps/%s/chat-conversations/%s", appId,conversationId);
        return executeRequestWithConsole("GET", path, new JSONObject(), null);

    }

    /**
     * 根据ID获取日志详细信息
     * @param appId
     * @param conversationId
     * @param limit
     * @return
     */
    public JSONObject getConversationsLogsForDetailInfo( String appId, String conversationId, Integer limit) {
        String path = String.format("/apps/%s/chat-conversations", appId);
        Map<String, Object> params = new HashMap<>();
        params.put("limit", limit);
        params.put("conversation_id", conversationId);
        return executeRequestWithConsole("GET", path, null, params);
    }


    /**
     * 根据ID获取工作流会话
     * @param appId
     * @param page
     * @param limit
     * @param status
     * @param keyword
     * @return
     */
    public JSONObject getWorkflowAppLogs(String appId, Integer page, Integer limit, String status, String keyword) {
        String path = String.format("/apps/%s/workflow-app-logs", appId);
        Map<String, Object> params = new HashMap<>();
        params.put("page", page);
        params.put("limit", limit);

        if (Strings.isNotBlank(status)) {
            params.put("status", status);
        }
        if (Strings.isNotBlank(keyword)) {
            params.put("keyword", keyword);
        }
        return executeRequestWithConsole("GET", path, null, params);
    }


    /**
     * 根据ID获取工作流日志详细信息
     * @param appId
     * @param workflowId
     * @return
     */
    public JSONObject getWorkflowRuns(String appId, String workflowId) {
        String path = String.format("/apps/%s/workflow-runs/%s", appId,workflowId);
        return executeRequestWithConsole("GET", path, new JSONObject(), null);
    }

    /**
     * 根据ID获取工作流执行情况
     * @param appId
     * @param workflowId
     * @return
     */
    public JSONObject getWorkflowRunsForNodeExecutions(String appId, String workflowId) {
        String path = String.format("/apps/%s/workflow-runs/%s/node-executions", appId,workflowId);
        return executeRequestWithConsole("GET", path, new JSONObject(), null);
    }

    private JSONObject executeRequestWithConsole(String method, String path, Object body, Map<String, Object> params) {
        return HttpUtils.executeAuthRequest(method, consoleUrl, path, body, params, consoleToken);
    }



}
