package com.ruoyi.model_deploy.service;

import java.util.List;
import com.ruoyi.model_deploy.domain.McpServerTool;

/**
 * MCP服务工具Service接口
 * 
 * <AUTHOR>
 */
public interface IMcpServerToolService 
{
    /**
     * 查询MCP服务工具
     * 
     * @param id MCP服务工具主键
     * @return MCP服务工具
     */
    public McpServerTool selectMcpServerToolById(Long id);

    /**
     * 查询MCP服务工具列表
     * 
     * @param mcpServerTool MCP服务工具
     * @return MCP服务工具集合
     */
    public List<McpServerTool> selectMcpServerToolList(McpServerTool mcpServerTool);

    /**
     * 新增MCP服务工具
     * 
     * @param mcpServerTool MCP服务工具
     * @return 结果
     */
    public int insertMcpServerTool(McpServerTool mcpServerTool);

    /**
     * 修改MCP服务工具
     * 
     * @param mcpServerTool MCP服务工具
     * @return 结果
     */
    public int updateMcpServerTool(McpServerTool mcpServerTool);

    /**
     * 删除MCP服务工具信息
     * 
     * @param id MCP服务工具主键
     * @return 结果
     */
    public int deleteMcpServerToolById(Long id);

    /**
     * 批量删除MCP服务工具
     * 
     * @param ids 需要删除的MCP服务工具主键集合
     * @return 结果
     */
    public int deleteMcpServerToolByIds(Long[] ids);
}