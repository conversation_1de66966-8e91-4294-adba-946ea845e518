package com.ruoyi.model_deploy;


import com.alibaba.fastjson2.JSONObject;

public class JsonUtils {

    public static String getNestedString(JSONObject json, String defaultVal, String... keys) {
        if (json == null || keys == null || keys.length == 0) {
            return defaultVal;
        }
        JSONObject current = json;
        for (int i = 0; i < keys.length - 1; i++) {
            Object obj = current.get(keys[i]);
            if (obj instanceof JSONObject) {
                current = (JSONObject) obj;
            } else {
                return defaultVal;
            }
        }
        return current.getString(keys[keys.length - 1]);
    }

}