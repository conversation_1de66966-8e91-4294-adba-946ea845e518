package com.ruoyi.model_deploy.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.result.CommonPage;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.model_deploy.domain.McpServerTool;
import com.ruoyi.model_deploy.domain.request.OptMcpToolRequest;
import com.ruoyi.model_deploy.domain.request.QueryMcpToolRequest;
import com.ruoyi.model_deploy.domain.vo.McpToolVO;
import com.ruoyi.model_deploy.service.IMcpServerToolService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * MCP服务工具Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mcp/server/tool")
@Tag(name = "MCP服务工具管理", description = "MCP服务工具相关接口")
public class McpServerToolController extends BaseController
{
    @Resource
    private IMcpServerToolService mcpServerToolService;

    /**
     * 查询MCP服务工具列表
     */
    @Operation(summary = "查询MCP服务工具列表")
    @GetMapping("/list")
    public R<List<McpToolVO>> list(QueryMcpToolRequest mcpServerTool){
        List<McpToolVO> list = mcpServerToolService.selectMcpServerToolList(mcpServerTool);
        return R.ok(list);
    }

    /**
     * 查询MCP服务工具列表（分页）
     */
    @Operation(summary = "查询MCP服务工具列表（分页）")
    @GetMapping("/page")
    public R<CommonPage<McpToolVO>> page(
            @RequestBody QueryMcpToolRequest mcpServerTool){
        startPage();
        List<McpToolVO> list = mcpServerToolService.selectMcpServerToolList(mcpServerTool);
        return R.ok(CommonPage.restPage(list));
    }

    /**
     * 获取MCP服务工具详细信息
     */
    @Operation(summary = "获取MCP服务工具详细信息")
    @GetMapping(value = "/{id}")
    public R<McpToolVO> getInfo(@Parameter(description = "MCP服务工具ID") @PathVariable("id") Long id) {
        return R.ok(mcpServerToolService.selectMcpServerToolById(id));
    }

    /**
     * 新增MCP服务工具
     */
    @Operation(summary = "新增MCP服务工具")
    @PostMapping
    public R<Integer> add(@RequestBody OptMcpToolRequest mcpServerTool) {
        return R.ok(mcpServerToolService.insertMcpServerTool(mcpServerTool));
    }

    /**
     * 修改MCP服务工具
     */
    @Operation(summary = "修改MCP服务工具")
    @PutMapping
    public R<Integer> edit(@RequestBody OptMcpToolRequest mcpServerTool) {
        return R.ok(mcpServerToolService.updateMcpServerTool(mcpServerTool));
    }

    /**
     * 删除MCP服务工具
     */
    @Operation(summary = "删除MCP服务工具")
    @DeleteMapping("/{id}")
    public R<Integer> remove(@Parameter(description = "MCP服务工具ID") @PathVariable("id") Long id) {
        return R.ok(mcpServerToolService.deleteMcpServerToolById(id));
    }
    

}
