package com.ruoyi.model_deploy.service;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.utils.http.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 知识库文档
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ModelWorkFlowsService {


    @Value("${knowledge.console-url}")
    private String consoleUrl;
    @Value("${knowledge.console-token}")
    private String consoleToken;


    /**
     * 获取模型列表
     *
     * @return
     */
    public JSONObject getLLM() {
        String path = String.format("/workspaces/current/models/model-types/llm");
        return executeRequestWithConsole("GET", path, null, null);
    }

    /**
     * 获取默认模型
     * @return
     */
    public JSONObject getDefaultModel() {
        String path = String.format("/workspaces/current/default-model");
        Map<String, Object> params = new HashMap<>();
        params.put("model_type", "llm");

        return executeRequestWithConsole("GET", path, null, params);
    }


    /**
     * 获取模型参数
     * @param provider
     * @param modelName
     * @return
     */
    public JSONObject getParameterRulesByModel(String provider,String modelName) {
        String path = String.format("/workspaces/current/model-providers/%s/models/parameter-rules",provider);
        Map<String, Object> params = new HashMap<>();
        params.put("model", modelName);

        return executeRequestWithConsole("GET", path, null, params);
    }


    private JSONObject executeRequestWithConsole(String method, String path, Object body, Map<String, Object> params) {
        return HttpUtils.executeAuthRequest(method, consoleUrl, path, body, params, consoleToken);
    }


}
