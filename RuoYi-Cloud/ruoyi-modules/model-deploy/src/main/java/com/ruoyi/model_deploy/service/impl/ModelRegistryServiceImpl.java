package com.ruoyi.model_deploy.service.impl;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.model_deploy.domain.ModelRegistry;
import com.ruoyi.model_deploy.mapper.ModelRegistryMapper;
import com.ruoyi.model_deploy.service.IModelRegistryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 模型注册表Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ModelRegistryServiceImpl extends ServiceImpl<ModelRegistryMapper, ModelRegistry> implements IModelRegistryService {
    @Resource
    private ModelRegistryMapper modelRegistryMapper;

    /**
     * 查询模型注册表
     *
     * @param modelId 模型注册表主键
     * @return 模型注册表
     */
    @Override
    public ModelRegistry selectModelRegistryById(Long modelId) {
        return modelRegistryMapper.selectById(modelId);
    }

    /**
     * 查询模型注册表列表
     *
     * @param modelRegistry 模型注册表
     * @return 模型注册表
     */
    @Override
    public IPage<ModelRegistry> selectModelRegistryList(ModelRegistry modelRegistry) {
        return modelRegistryMapper.selectPage(new Page<>(modelRegistry.getPageNum(), modelRegistry.getPageSize()), Wrappers.<ModelRegistry>lambdaQuery()
                .eq(null != modelRegistry.getStatus(), ModelRegistry::getStatus, modelRegistry.getStatus())
                .like(StringUtils.isNotBlank(modelRegistry.getModelName()), ModelRegistry::getModelName, modelRegistry.getModelName())
                .like(StringUtils.isNotBlank(modelRegistry.getCustomModelName()), ModelRegistry::getCustomModelName, modelRegistry.getCustomModelName())
                .eq(null != modelRegistry.getIsGguf(), ModelRegistry::getIsGguf, modelRegistry.getIsGguf())
                .and(m -> m.eq(ModelRegistry::getIsPublic, true).or().eq(ModelRegistry::getCreatedBy, modelRegistry.getCreatedBy())));
    }

    /**
     * 新增模型注册表
     *
     * @param modelRegistry 模型注册表
     * @return 结果
     */
    @Override
    public int insertModelRegistry(ModelRegistry modelRegistry) {
        return modelRegistryMapper.insert(modelRegistry);
    }

    /**
     * 修改模型注册表
     *
     * @param modelRegistry 模型注册表
     * @return 结果
     */
    @Override
    public int updateModelRegistry(ModelRegistry modelRegistry) {
        log.info("修改模型注册表:{}", modelRegistry);
        modelRegistryMapper.update(null, Wrappers.<ModelRegistry>lambdaUpdate()
                .eq(ModelRegistry::getModelRegistryId, modelRegistry.getModelRegistryId())
                .set(null != modelRegistry.getIsPublic(), ModelRegistry::getIsPublic, modelRegistry.getIsPublic())
                .set(StringUtils.isNotBlank(modelRegistry.getDescription()), ModelRegistry::getDescription, modelRegistry.getDescription())
                .set(StringUtils.isNotBlank(modelRegistry.getCustomModelName()), ModelRegistry::getCustomModelName, modelRegistry.getCustomModelName())
                .set(StringUtils.isNotBlank(modelRegistry.getStoragePath()), ModelRegistry::getStoragePath, modelRegistry.getStoragePath())
                .set(null != modelRegistry.getStatus(), ModelRegistry::getStatus, modelRegistry.getStatus())
                .set(null != modelRegistry.getUpdateAt(), ModelRegistry::getUpdateAt, modelRegistry.getUpdateAt()));
        return 1;
    }

    /**
     * 批量删除模型注册表
     *
     * @param modelIds 需要删除的模型注册表主键
     * @return 结果
     */
    @Override
    public int deleteModelRegistryByIds(Long[] modelIds) {
        return modelRegistryMapper.deleteModelRegistryByIds(modelIds);
    }

    /**
     * 删除模型注册表信息
     *
     * @param modelId 模型注册表主键
     * @return 结果
     */
    @Override
    public int deleteModelRegistryById(Long modelId) {
        return modelRegistryMapper.deleteById(modelId);
    }
}