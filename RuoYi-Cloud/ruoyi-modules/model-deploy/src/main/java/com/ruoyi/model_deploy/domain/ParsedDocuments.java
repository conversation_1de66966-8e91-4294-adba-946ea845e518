package com.ruoyi.model_deploy.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 文件解析表 ParsedDocuments
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ParsedDocuments extends BaseModelEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId
    private Integer documentId;

    /**
     * 原始PDF的minio URL
     */
    @Excel(name = "原始PDF的minio URL")
    private String originalPdfUrl;

    /**
     * 所属用户ID
     */
    @Excel(name = "所属用户ID")
    private Integer ownerId;

    /**
     * 解析方式(mpdf)
     */
    @Excel(name = "解析方式")
    private ParsedType parsedType;

    /**
     * 解析后PDF的minio URL
     */
    @Excel(name = "解析后PDF的minio URL")
    private String parsedPdfUrl;

    /**
     * 解析状态
     */
    @Excel(name = "解析状态")
    private ParseStatus parseStatus;



    /**
     * parsedType枚举
     */
    public enum ParsedType {
        mineru
    }

    /**
     * ParseStatus枚举
     */
    public enum ParseStatus {
        pending,
        downloading,
        parsing,
        uploading,
        finished,
        failed
    }
}
