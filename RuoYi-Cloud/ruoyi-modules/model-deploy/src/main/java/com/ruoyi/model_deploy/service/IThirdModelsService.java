package com.ruoyi.model_deploy.service;


import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.http.HttpUtils;
import com.ruoyi.model_deploy.JsonUtils;
import com.ruoyi.model_deploy.domain.dto.LoadBalancing;
import com.ruoyi.model_deploy.domain.request.AccessModelReq;
import com.ruoyi.model_deploy.domain.request.ChildModeRequest;
import com.ruoyi.model_deploy.domain.request.ChildModsRequest;
import com.ruoyi.model_deploy.domain.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.ruoyi.model_deploy.constant.ThirdModelsUrl.MODEL_PROVIDERS_BASE_URL;

@Slf4j
@Service
public class IThirdModelsService {


    @Value("${knowledge.console-url}")
    private String consoleUrl;
    @Value("${knowledge.console-token}")
    private String consoleToken;


    public List<ThirdModelsVO> getProviders() {
        List<ThirdModelsVO> thirdModelsVOS = new ArrayList<>();
        JSONObject object = executeRequestWithConsole("GET", MODEL_PROVIDERS_BASE_URL, null, null);
        JSONArray orgData = object.getJSONArray("data");
        for (int i = 0; i < orgData.size(); i++) {
            ThirdModelsVO modelsVO = new ThirdModelsVO();
            JSONObject json = orgData.getJSONObject(i);
            modelsVO.setProvider(json.getString("provider"));
            modelsVO.setLabel(JsonUtils.getNestedString(json, null, "label", "zh_Hans"));
            modelsVO.setConfigurateMethods(json.getList("configurate_methods", String.class));
            modelsVO.setCustomConfiguration(JsonUtils.getNestedString(json, null, "custom_configuration", "status"));
            modelsVO.setDescription(JsonUtils.getNestedString(json, null, "description", "zh_Hans"));
            modelsVO.setHelpWords(JsonUtils.getNestedString(json, null, "help", "title", "zh_Hans"));
            modelsVO.setHelpUrl(JsonUtils.getNestedString(json, null, "help", "url", "zh_Hans"));
            modelsVO.setCustomConfigurationStatus(JsonUtils.getNestedString(json, null, "custom_configuration", "status"));
            modelsVO.setSystemConfiguration(json.getJSONObject("system_configuration"));
            JSONObject modelCredentialSchema = json.getJSONObject("model_credential_schema");
            JSONObject providerCredentialSchema = json.getJSONObject("provider_credential_schema");
            if (modelCredentialSchema != null) {
                ModelCredentialSchemaVO modelCredentialSchemaVO = new ModelCredentialSchemaVO();
                List<CredentialFormSchemasVO> modelCredentialSchemaVOS = new ArrayList<>();
                setCredential(modelCredentialSchema, modelCredentialSchemaVOS);

                ModelFormVO formVO = new ModelFormVO();
                formVO.setLabel(JsonUtils.getNestedString(modelCredentialSchema, null, "model", "label", "zh_Hans"));
                formVO.setPlaceholder(JsonUtils.getNestedString(modelCredentialSchema, null, "model", "placeholder", "zh_Hans"));
                modelCredentialSchemaVO.setModel(formVO);
                modelCredentialSchemaVO.setCredentialFormSchemas(modelCredentialSchemaVOS);
                modelsVO.setModelCredentialSchema(modelCredentialSchemaVO);
            }
            if (providerCredentialSchema != null) {
                ProviderCredentialSchemaVO providerCredentialSchemaVO = new ProviderCredentialSchemaVO();
                List<CredentialFormSchemasVO> credentialFormSchemasVOS = new ArrayList<>();
                setCredential(providerCredentialSchema, credentialFormSchemasVOS);
                providerCredentialSchemaVO.setCredentialFormSchemas(credentialFormSchemasVOS);
                modelsVO.setProviderCredentialSchema(providerCredentialSchemaVO);
            }
            modelsVO.setSupportedModelTypes(json.getList("supported_model_types", String.class));
            thirdModelsVOS.add(modelsVO);
        }

        return thirdModelsVOS;
    }

    private static void setCredential(JSONObject modelCredentialSchema, List<CredentialFormSchemasVO> modelCredentialSchemaVOS) {

        modelCredentialSchema.getJSONArray("credential_form_schemas").forEach(item -> {
            CredentialFormSchemasVO credentialFormSchemasVO = new CredentialFormSchemasVO();
            JSONObject json1 = (JSONObject) item;
            credentialFormSchemasVO.setDefaultValue(json1.getString("default"));
            credentialFormSchemasVO.setLabel(JsonUtils.getNestedString(json1, null, "label", "zh_Hans"));
            credentialFormSchemasVO.setMaxLength(json1.getString("max_length"));
            credentialFormSchemasVO.setOptions(json1.getString("options"));
            credentialFormSchemasVO.setPlaceholder(JsonUtils.getNestedString(json1, null, "placeholder", "zh_Hans"));
            credentialFormSchemasVO.setRequired(json1.getString("required"));
            credentialFormSchemasVO.setType(json1.getString("type"));
            credentialFormSchemasVO.setVariable(json1.getString("variable"));
            modelCredentialSchemaVOS.add(credentialFormSchemasVO);
        });
    }


    private static final List<String> order = Arrays.asList("llm", "text-embedding", "Speech2text", "moderation", "tts");

    public List<ChildModelVO> getChildModels(ChildModeRequest request) {
        JSONObject object = executeRequestWithConsole("GET", MODEL_PROVIDERS_BASE_URL + "/" + request.getProvider() + "/models", null, null);
        JSONArray orgData = object.getJSONArray("data");
        List<ChildModelVO> childModelVOS = new ArrayList<>();
        for (int i = 0; i < orgData.size(); i++) {
            ChildModelVO childModelVO = new ChildModelVO();
            JSONObject json = orgData.getJSONObject(i);
            childModelVO.setDeprecated(json.getBoolean("deprecated"));
            childModelVO.setFeatures(json.getList("features", String.class));
            childModelVO.setFetchFrom(json.getString("fetch_from"));
            childModelVO.setLabel(JsonUtils.getNestedString(json, null, "label", "zh_Hans"));
            childModelVO.setLoadBalancingEnabled(json.getBoolean("load_balancing_enabled"));
            childModelVO.setModel(json.getString("model"));
            childModelVO.setModelProperties(json.getJSONObject("model_properties"));
            childModelVO.setModelType(json.getString("model_type"));
            childModelVO.setStatus(json.getString("status"));
            childModelVOS.add(childModelVO);
        }
        if (StringUtils.isNotBlank(request.getModel())) {
            log.info("子模型名称{}", request.getModel());
            childModelVOS = childModelVOS.stream()
                    .filter(r -> r.getModel().contains(request.getModel())).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(childModelVOS)) {
                return Collections.emptyList();
            }
        }
        // 如果名称不在排序顺序中，则放在最后
        return childModelVOS.stream().filter(r -> r.getDeprecated().equals(false))
                .sorted(Comparator.comparingInt(p -> {
                    int index = order.indexOf(p.getModelType());
                    // 如果名称不在排序顺序中，则放在最后
                    return index == -1 ? Integer.MAX_VALUE : index;
                })).collect(Collectors.toList());
    }

    public R enable(ChildModsRequest request) {
        log.info("启用子模型{}", JSONObject.toJSONString(request));
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("model", request.getModel());
        jsonObject.put("model_type", request.getModelType());
        JSONObject resp = executeRequestWithConsole("PATCH", MODEL_PROVIDERS_BASE_URL + "/" + request.getProvider() + "/models/enable", jsonObject, null);
        if (resp.get("result").equals("success")) {
            return R.ok();
        }
        return R.fail();
    }


    public R disable(ChildModsRequest request) {
        log.info("停用子模型{}", JSONObject.toJSONString(request));
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("model", request.getModel());
        jsonObject.put("model_type", request.getModelType());
        JSONObject resp = executeRequestWithConsole("PATCH", MODEL_PROVIDERS_BASE_URL + "/" + request.getProvider() + "/models/disable", jsonObject, null);
        if (resp.get("result").equals("success")) {
            return R.ok();
        }
        return R.fail();
    }

    public JSONObject getCredentials(String provider) {
        JSONObject object = executeRequestWithConsole("GET", MODEL_PROVIDERS_BASE_URL + "/" + provider + "/credentials", null, null);
        return object.getJSONObject("credentials");
    }


    public R accessModel(AccessModelReq req) {
        log.info("引入本地模型{}", JSONObject.toJSONString(req));
        JSONObject body = new JSONObject();
        body.put("config_from", req.getConfig_from());
        body.put("credentials", req.getCredentials());
        LoadBalancing loadBalancing = new LoadBalancing();
        body.put("load_balancing", loadBalancing);
        JSONObject object = new JSONObject();
        if (req.getModel() != null && req.getModel_type() != null) {
            body.put("model", req.getModel());
            body.put("model_type", req.getModel_type());
            object = executeRequestWithConsole("POST", MODEL_PROVIDERS_BASE_URL + "/" + req.getProvider() + "/" + "models", body, null);

        } else {
            object = executeRequestWithConsole("POST", MODEL_PROVIDERS_BASE_URL + "/" + req.getProvider(), body, null);
        }
        String result = object.getString("result");
        if (result.equals("success")) {
            return R.ok();
        }
        return R.fail();

    }


    public void delModels(String provider) {
        log.info("移除本地模型{}", provider);
        executeRequestWithConsole("DELETE", MODEL_PROVIDERS_BASE_URL + "/" + provider, null, null);
    }


    private JSONObject executeRequestWithConsole(String method, String path, Object body, Map<String, Object> params) {
        return HttpUtils.executeAuthRequest(method, consoleUrl, path, body, params, consoleToken);
    }

}
