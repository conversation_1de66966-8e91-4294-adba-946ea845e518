package com.ruoyi.model_deploy.service;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.utils.http.HttpUtils;
import com.ruoyi.model_deploy.config.MinioUtils;
import com.ruoyi.model_deploy.domain.dto.DocumentsDto;
import com.ruoyi.model_deploy.domain.dto.KnowledgeListDto;
import com.ruoyi.model_deploy.mapper.ParsedDocumentsMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 知识库文档
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DocumentsService {

    @Value("${knowledge.api-url}")
    private String baseUrl;
    @Value("${knowledge.api-key}")
    private String apiKey;
    @Value("${knowledge.console-url}")
    private String consoleUrl;
    @Value("${knowledge.console-token}")
    private String consoleToken;
    @Resource
    private MinioUtils minioUtils;


    @Resource
    private ParsedDocumentsMapper parsedDocumentsMapper;

    /**
     * 查询知识库文档列表
     */
    public JSONObject list(String datasetId,KnowledgeListDto dto) {
        Map<String, Object> params = new HashMap<>();
        params.put("page", dto.getPage());
        params.put("limit", dto.getLimit());
        if (Strings.isNotBlank(dto.getKeyword())) {
            params.put("keyword", dto.getKeyword());
        }
        String path = String.format("/datasets/%s/documents", datasetId);
        return executeRequestWithApiKey("GET", path, null, params,null);
    }


    /**
     * 删除知识库文档
     */
    public JSONObject delete(String datasetId, String documentId) {
        String path = String.format("/datasets/%s/documents/%s", datasetId,documentId);
        return executeRequestWithApiKey("DELETE", path, new JSONObject(), null,null);
    }


    /**
     * 获取文档嵌入状态（进度）
     */
    public JSONObject getStatusForDocument(String datasetId, String documentId) {
        String path = String.format("/datasets/%s/documents/%s/indexing-status", datasetId, documentId);
        return executeRequestWithApiKey("GET", path, new JSONObject(), null,null);
    }

    /**
     * 通过文件创建文档
     */
    public JSONObject createDocumentByFile(String datasetId, MultipartFile file,DocumentsDto dto) throws Exception {
        String path = String.format("/datasets/%s/document/create-by-file",datasetId);
        ObjectMapper objectMapper = new ObjectMapper();
        JSONObject body = JSONObject.parse(objectMapper.writeValueAsString(dto));
        return executeRequestWithApiKey("POSTFORMDATA", path, body, null,file);
    }

    /**
     * 通过文件更新文档
     */
    public JSONObject updateDocumentByFile(String datasetId,String ducumentId, MultipartFile file,DocumentsDto dto) throws JsonProcessingException {
        String path = String.format("/datasets/%s/documents/%s/update-by-file",datasetId,ducumentId);
        ObjectMapper objectMapper = new ObjectMapper();
        JSONObject body = JSONObject.parse(objectMapper.writeValueAsString(dto));
        return executeRequestWithApiKey("POSTFORMDATA", path,body, null,file);
    }


    /**
     * 上传文件
     * @param file 文件
     * @return
     */
    public JSONObject uploadFiles(MultipartFile file) {
        String path = "/files/upload";
        return executeRequestWithConsole("POSTFORMDATA", path, null, null,file);
    }

    /**
     * 文件预览
     * @param dto
     * @return
     */

    public JSONObject indexingEstimate(DocumentsDto dto) throws JsonProcessingException {
        String path = "/datasets/indexing-estimate";
        ObjectMapper objectMapper = new ObjectMapper();
        JSONObject body = JSONObject.parse(objectMapper.writeValueAsString(dto));
        return executeRequestWithConsole("POST", path,body, null,null);
    }

    /**
     * 文档切片
     * @param datasetId
     * @param dto
     * @return
     */
    public Object sliceDocuments(String datasetId, DocumentsDto dto) throws JsonProcessingException {
        String path = String.format("/datasets/%s/documents",datasetId);
        ObjectMapper objectMapper = new ObjectMapper();
        JSONObject body = JSONObject.parse(objectMapper.writeValueAsString(dto));
        return executeRequestWithConsole("POST", path,body, null,null);
    }

    /**
     * 执行HTTP请求的通用方法
     *
     * @param method HTTP方法
     * @param path   请求路径
     * @param body   请求体
     * @param params 查询参数
     * @return 响应结果
     */
    private JSONObject executeRequestWithApiKey(String method, String path, Object body, Map<String, Object> params, MultipartFile file) {
        return HttpUtils.executeAuthRequest(method, baseUrl, path, body, params, apiKey);
    }

    private JSONObject executeRequestWithConsole(String method, String path, Object body, Map<String, Object> params, MultipartFile file) {
        return HttpUtils.executeAuthRequest(method, consoleUrl, path, body, params, consoleToken);
    }


}
