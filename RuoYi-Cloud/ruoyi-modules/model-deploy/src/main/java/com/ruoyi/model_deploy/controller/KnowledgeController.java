package com.ruoyi.model_deploy.controller;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.model_deploy.domain.dto.*;
import com.ruoyi.model_deploy.service.KnowledgeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 知识库Controller
 *
 * <AUTHOR>
 */
@Tag(name = "知识库管理", description = "知识库相关接口")
@RestController
@Slf4j
@RequestMapping("/knowledge")
public class KnowledgeController extends BaseController {

    @Resource
    private KnowledgeService knowledgeService;

    @Operation(summary = "创建空知识库", description = "创建一个新的空知识库")
    @PostMapping("/datasets")
    public R add(@Parameter(description = "知识库信息") @RequestBody JSONObject dto) {
        return R.ok(knowledgeService.add(dto));
    }

    @Operation(summary = "查询知识库列表", description = "获取所有知识库列表信息")
    @GetMapping("/list")
    public R list(@Parameter(description = "查询参数") KnowledgeListDto dto) {
        return R.ok(knowledgeService.list(dto));
    }

    @Operation(summary = "删除知识库", description = "根据知识库ID删除指定知识库")
    @DeleteMapping("/datasets/{datasetId}")
    public R remove(@Parameter(description = "知识库ID") @PathVariable String datasetId) {
        return R.ok(knowledgeService.remove(datasetId));
    }

    @Operation(summary = "更新知识库设置", description = "更新指定知识库的配置信息")
    @PatchMapping("/datasets/{datasetId}")
    public R updateSettings(
            @Parameter(description = "知识库ID") @PathVariable String datasetId,
            @Parameter(description = "知识库设置信息") @RequestBody KnowledgeSettingsDto dto) {
        return R.ok(knowledgeService.updateSettings(datasetId, dto));
    }

    @Operation(summary = "新增分段", description = "向指定文档添加新的分段信息")
    @PostMapping("/datasets/{datasetId}/documents/{documentId}/segments")
    public R addSegments(
            @Parameter(description = "知识库ID") @PathVariable String datasetId,
            @Parameter(description = "文档ID") @PathVariable String documentId,
            @Parameter(description = "分段信息") @RequestBody AddSegmentsDto dto) {
        return R.ok(knowledgeService.addSegments(datasetId, documentId, dto));
    }

    @Operation(summary = "查询文档分段", description = "获取指定文档的分段信息")
    @GetMapping("/datasets/{datasetId}/documents/{documentId}/segments")
    public R getSegments(
            @Parameter(description = "知识库ID") @PathVariable String datasetId,
            @Parameter(description = "文档ID") @PathVariable String documentId,
            @Parameter(description = "关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "状态") @RequestParam(required = false) String enabled,
            @Parameter(description = "页数") @RequestParam Integer page,
            @Parameter(description = "每页大小") @RequestParam Integer limit) {
        return R.ok(knowledgeService.getSegments(datasetId, documentId, keyword, enabled, page, limit));
    }

    @Operation(summary = "删除文档分段", description = "删除指定的文档分段")
    @DeleteMapping("/datasets/{datasetId}/documents/{documentId}/segments/{segmentId}")
    public R delSegments(
            @Parameter(description = "知识库ID") @PathVariable String datasetId,
            @Parameter(description = "文档ID") @PathVariable String documentId,
            @Parameter(description = "分段ID") @PathVariable String segmentId) {
        return R.ok(knowledgeService.delSegments(datasetId, documentId, segmentId));
    }

    @Operation(summary = "更新文档分段", description = "更新指定的文档分段信息")
    @PostMapping("/datasets/{datasetId}/documents/{documentId}/segments/{segmentId}")
    public R updateSegments(
            @Parameter(description = "知识库ID") @PathVariable String datasetId,
            @Parameter(description = "文档ID") @PathVariable String documentId,
            @Parameter(description = "分段ID") @PathVariable String segmentId,
            @Parameter(description = "更新的分段信息") @RequestBody AddSegmentsDto dto) {
        return R.ok(knowledgeService.updateSegments(datasetId, documentId, segmentId, dto));
    }

    @Operation(summary = "更新文档分段状态", description = "更新指定的文档分段状态启用或禁用")
    @PutMapping("/datasets/{datasetId}/documents/{documentId}/segment/{action}")
    public R updateSegmentsStatus(
            @Parameter(description = "知识库ID") @PathVariable String datasetId,
            @Parameter(description = "文档ID") @PathVariable String documentId,
            @Parameter(description = "状态enable或disable") @PathVariable String action,
            @Parameter(description = "分段ID") @RequestParam String segmentId) {
        return R.ok(knowledgeService.updateSegmentsStatus(datasetId, documentId, action, segmentId));
    }

    @Operation(summary = "查询模型", description = "获取指定类型的模型信息")
    @GetMapping("/model-types/{modelType}")
    public R getModels(@Parameter(description = "模型类型") @PathVariable String modelType) {
        return R.ok(knowledgeService.getModels(modelType));
    }

    @Operation(summary = "查询工作流参数规则", description = "获取指定模型提供者和模型的参数规则信息")
    @GetMapping("/model-providers/{provider}/models/parameter-rules")
    public R getParameterRules(
            @Parameter(description = "模型提供者") @PathVariable String provider,
            @Parameter(description = "模型名称") @RequestParam String model) {
        return R.ok(knowledgeService.getParameterRules(provider, model));
    }

    @Operation(summary = "获取默认模型", description = "获取指定类型的默认模型信息")
    @GetMapping("/workspaces/current/default-model")
    public R getDefaultModel(@Parameter(description = "模型类型") @RequestParam("model_type") String modelType) {
        return R.ok(knowledgeService.getDefaultModel(modelType));
    }
}