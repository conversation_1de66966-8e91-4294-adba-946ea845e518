{"mcpServers": {"baidu-map": {"command": "cmd", "args": ["/c", "npx", "-y", "@baidumap/mcp-server-baidu-map"], "env": {"BAIDU_MAP_API_KEY": "LEyBQxG9UzR9C1GZ6zDHsFDVKvBem2do"}}, "filesystem": {"command": "cmd", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-filesystem", "C:/Users/<USER>/Desktop"]}, "mcp-server-weather": {"command": "java", "args": ["-Dspring.ai.mcp.server.stdio=true", "-Dlogging.pattern.console=", "-jar", "D:\\ideaworkspace\\git_pull\\tuling-flight-booking_all\\mcp-stdio-server\\target\\mcp-stdio-server-xs-1.0.jar"]}}}