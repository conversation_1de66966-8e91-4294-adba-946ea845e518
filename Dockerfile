FROM openjdk:17
LABEL author="LMY233"
VOLUME /home/<USER>/logs/dream-admin
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
COPY /dream-admin-1.0.0.jar /dream-admin-1.0.0.jar
EXPOSE 12308
ENTRYPOINT ["java","-jar","/dream-admin-1.0.0.jar","--spring.profiles.active=test","-Duser.timezone=GMT+8"]



docker run -d \
 -v /home/<USER>/logs/dream-admin:/home/<USER>/logs/dream-admin \
 -p 12308:12308 \
 dream-admin:v1.0