# Spring Boot 集成 Nacos 简单测试指南

## 当前配置说明

### 1. 依赖配置 (pom.xml)
```xml
<dependency>
    <groupId>com.alibaba.boot</groupId>
    <artifactId>nacos-config-spring-boot-starter</artifactId>
    <version>0.2.12</version>
</dependency>
```

### 2. 应用配置 (application.properties)
```properties
# Spring Boot 应用配置
spring.application.name=nacos-demo
server.port=8080

# Nacos 配置中心设置
nacos.config.server-addr=127.0.0.1:8848
nacos.config.data-id=nacos-demo
nacos.config.group=DEFAULT_GROUP
nacos.config.type=properties
nacos.config.auto-refresh=true

# 日志配置
logging.level.com.lmy.nacos=debug
logging.level.com.alibaba.nacos=debug
```

### 3. Controller 中的 @Value 注解
```java
@RestController
public class TestController {

    @Value("${server.port.message:默认端口消息}")
    private String serverPortMessage;

    @Value("${test.message:默认测试消息}")
    private String testMessage;

    @GetMapping("/message")
    public String getMessage() {
        return "Test Message: " + testMessage;
    }
    
    @GetMapping("/port-message")
    public String getPortMessage() {
        return "Server Port Message: " + serverPortMessage;
    }
}
```

## 测试步骤

### 步骤 1: 确认 Nacos 配置
根据您的测试，Nacos 中应该有以下配置：
- Data ID: `nacos-demo`
- Group: `DEFAULT_GROUP`
- 配置内容:
```properties
server.port.message=8088
test.message=1111
```

### 步骤 2: 编译和启动应用
```bash
mvn clean compile
mvn spring-boot:run
```

### 步骤 3: 测试接口
```bash
# 测试 test.message 配置
curl http://localhost:8080/message
# 期望结果: Test Message: 1111

# 测试 server.port.message 配置
curl http://localhost:8080/port-message
# 期望结果: Server Port Message: 8088

# 测试所有配置
curl http://localhost:8080/all-config
# 期望结果: 显示所有从 Nacos 读取的配置
```

## 预期结果

如果配置正确，您应该看到：
- `/message` 返回 "Test Message: 1111"（而不是默认值）
- `/port-message` 返回 "Server Port Message: 8088"（而不是默认值）

## 故障排除

### 如果仍然返回默认值：

1. **检查 Nacos 服务器状态**
   ```bash
   curl http://127.0.0.1:8848/nacos/v1/console/health/readiness
   ```

2. **检查应用启动日志**
   查找类似以下的日志：
   ```
   [INFO] Loading nacos data, dataId: 'nacos-demo', group: 'DEFAULT_GROUP'
   ```

3. **验证 Nacos 配置**
   - 访问 http://127.0.0.1:8848/nacos
   - 确认 Data ID 为 `nacos-demo` 的配置存在
   - 确认配置内容包含 `server.port.message=8088` 和 `test.message=1111`

4. **检查配置格式**
   - 确保 Nacos 中的配置格式为 Properties
   - 确保没有多余的空格或特殊字符

## 成功标志

当配置成功时，您会看到：
1. 应用启动时连接到 Nacos 服务器
2. 接口返回 Nacos 中配置的实际值
3. 修改 Nacos 配置后，应用自动获取新值（无需重启）
