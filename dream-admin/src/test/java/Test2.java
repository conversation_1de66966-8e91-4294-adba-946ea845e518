import cn.dream.blade.domain.entity.BoxInfo;
import cn.dream.blade.mapper.BoxInfoMapper;
import jakarta.annotation.Resource;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
public class Test2 {

    @Resource
    private BoxInfoMapper boxInfoMapper;



    @Test
    public void test(){
        BoxInfo boxInfo = new BoxInfo();
        boxInfo.setId("1");

        boxInfoMapper.updateOrInsert2(List.of(boxInfo));
    }
}
