package cn.dream.web.controller.bladeModel;

import cn.dream.blade.domain.BoxProduct;
import cn.dream.blade.domain.request.BoxProductQueryRequest;
import cn.dream.blade.service.IBoxProductService;
import cn.dream.common.annotation.Anonymous;
import cn.dream.common.annotation.Log;
import cn.dream.common.core.controller.BaseController;
import cn.dream.common.core.page.TableDataInfo;
import cn.dream.common.enums.BusinessType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 盒子产品Controller
 *
 * <AUTHOR>
 */
@Tag(name = "盒子产品接口", description = "用于管理盒子产品信息，包括推荐产品和最终产品")
@RestController
@RequestMapping("/back/blade/boxProduct")
@Anonymous
@Slf4j
public class BoxProductBackController extends BaseController {
    @Resource
    private IBoxProductService boxProductService;

    /**
     * 分页查询盒子产品列表
     */
    @PostMapping("/list")
    @Operation(summary = "分页查询盒子产品列表")
    public TableDataInfo list(BoxProductQueryRequest request) {
        startPage();
        // 执行查询
        List<BoxProduct> list = boxProductService.selectBoxProductList(request);
        // 返回分页数据
        return getDataTable(list);
    }
}
