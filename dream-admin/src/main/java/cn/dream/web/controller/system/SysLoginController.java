package cn.dream.web.controller.system;

import cn.dream.common.constant.Constants;
import cn.dream.common.core.domain.AjaxResult;
import cn.dream.common.core.domain.AjaxResultMap;
import cn.dream.common.core.domain.entity.SysMenu;
import cn.dream.common.core.domain.entity.SysUser;
import cn.dream.common.core.domain.model.LoginBody;
import cn.dream.common.core.domain.model.LoginUser;
import cn.dream.common.core.domain.vo.UserInfoVo;
import cn.dream.common.utils.SecurityUtils;
import cn.dream.framework.web.service.SysLoginService;
import cn.dream.framework.web.service.SysPermissionService;
import cn.dream.framework.web.service.TokenService;
import cn.dream.system.domain.vo.RouterVo;
import cn.dream.system.service.ISysMenuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@Tag(name = "登录验证")
@RestController
public class SysLoginController {
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private TokenService tokenService;

    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @Operation(summary = "用户登录")
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody) {
        // 生成令牌
        String token = loginService.login(loginBody.getUserName(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid());
        AjaxResultMap map = AjaxResultMap.success();
        map.put(Constants.TOKEN, token);
        return AjaxResult.success(map);
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @Operation(summary = "获取用户信息")
    @GetMapping("getInfo")
    public AjaxResult<UserInfoVo> getInfo() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        if (!loginUser.getPermissions().equals(permissions)) {
            loginUser.setPermissions(permissions);
            tokenService.refreshToken(loginUser);
        }
        UserInfoVo userInfoVo = new UserInfoVo(user, roles, permissions);
        return AjaxResult.success(userInfoVo);
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @Operation(summary = "获取路由信息")
    @GetMapping("getRouters")
    public AjaxResult<List<RouterVo>> getRouters() {
        Long userId = SecurityUtils.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return AjaxResult.success(menuService.buildMenus(menus));
    }
}
