package cn.dream.web.controller.system;

import cn.dream.common.constant.Constants;
import cn.dream.common.core.domain.AjaxResult;
import cn.dream.common.core.domain.AjaxResultMap;
import cn.dream.common.core.domain.model.SmsLoginBody;
import cn.dream.framework.web.service.SysLoginService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 手机验证码登录验证
 *
 * <AUTHOR>
 */
@Tag(name = "手机验证码登录验证")
@RestController
public class SmsLoginController {
    @Autowired
    private SysLoginService loginService;


    /**
     * 发送手机验证码
     *
     * @param mobile 手机验证码登录信息
     * @return 结果
     */
    @PostMapping("/sendSms")
    public AjaxResult sendSms(@RequestBody String mobile) {
        Integer status = loginService.sendSms(mobile);
        if (status.equals(1)) {
            return AjaxResult.success("发送成功，请注意查收");
        } else {
            return AjaxResult.error("发送失败，稍后请重试");
        }
    }

    /**
     * 验证码登录方法
     *
     * @param smsLoginBody 手机验证码登录信息
     * @return 结果
     */
    @PostMapping("/smsLogin")
    public AjaxResultMap smsLogin(@RequestBody SmsLoginBody smsLoginBody) {
        String phonenumber = smsLoginBody.getPhonenumber();
        String code = smsLoginBody.getCode();
        // 先判断验证码是否正确
        if (loginService.validateSmsCode(phonenumber, code)) {
            AjaxResultMap ajax = AjaxResultMap.success();
            //生成令牌
            String token = loginService.smsLogin(phonenumber, code);
            ajax.put(Constants.TOKEN, token);
            return ajax;
        } else {
            return AjaxResultMap.error("验证码错误");
        }
    }
}
