package cn.dream.web.controller.bladeModel;


import cn.dream.blade.service.IBoxInfoService;
import cn.dream.common.core.domain.AjaxResultMap;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/back/blade/boxInfo")
@Tag(name = "后管-盒型模块", description = "用于管理盒型数据信息")
public class BoxInfoBackController {

    @Resource
    private IBoxInfoService boxInfoService;


    @PostMapping("/analysisExcel")
    @Operation(summary = "解析盒型excel并保存初始盒型数据")
    public AjaxResultMap analysisExcel(@RequestParam("file") MultipartFile file) {
        boxInfoService.analysisExcel(file);
        return AjaxResultMap.success();
    }


}


{
        "registry-mirrors": [
        "https://2a6bf1988cb6428c877f723ec7530dbc.mirror.swr.myhuaweicloud.com",
        "https://docker.m.daocloud.io",
        "https://hub-mirror.c.163.com",
        "https://mirror.baidubce.com",
        "https://your_preferred_mirror",
        "https://dockerhub.icu",
        "https://docker.registry.cyou",
        "https://docker-cf.registry.cyou",
        "https://dockercf.jsdelivr.fyi",
        "https://docker.jsdelivr.fyi",
        "https://dockertest.jsdelivr.fyi",
        "https://mirror.aliyuncs.com",
        "https://dockerproxy.com",
        "https://mirror.baidubce.com",
        "https://docker.m.daocloud.io",
        "https://docker.nju.edu.cn",
        "https://docker.mirrors.sjtug.sjtu.edu.cn",
        "https://docker.mirrors.ustc.edu.cn",
        "https://mirror.iscas.ac.cn",
        "https://docker.rainbond.cc"
        ]
        }
