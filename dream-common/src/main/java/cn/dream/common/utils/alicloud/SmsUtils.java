package cn.dream.common.utils.alicloud;

import cn.dream.common.constant.SmsConstants;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

import static cn.dream.common.utils.alicloud.SendSms.*;
import static com.aliyun.teautil.Common.assertAsString;

/**
 * 阿里云短信服务工具类
 */
@Slf4j
public class SmsUtils {
    public static Client createClient() throws Exception {
        Config config = new Config()
                // 配置 AccessKey ID，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。
                .setAccessKeyId(System.getenv(SmsConstants.ACCESS_KEY_ID))
                // 配置 AccessKey Secret，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
                .setAccessKeySecret(System.getenv(SmsConstants.ACCESS_KEY_SECRET));

        // 配置 Endpoint
        config.endpoint = SmsConstants.END_POINT;

        return new Client(config);
    }

    /**
     * 发送验证码短信
     *
     * @param phone
     * @param code
     * @return
     * @throws Exception
     */
    public static Map<String, Object> sendMsg(String phone, String code) throws Exception {
//        SendCode(phone,code);
        // 所以构造Map类型的返回
        HashMap<String, Object> sendSmsResponseMap = new HashMap<>();
        try {
            // 初始化请求客户端
            Client client = SmsUtils.createClient();

            // 构造请求对象，请填入请求参数值
            SendSmsRequest sendSmsRequest = new SendSmsRequest()
                    .setPhoneNumbers(phone)
                    .setSignName(SmsConstants.SIGN_NAME)
                    .setTemplateCode(SmsConstants.TEMPLATE_CODE)
                    .setTemplateParam("{code:" + code + "}");


            // 获取响应对象,响应包含服务端响应的 body 和 headers
            SendSmsResponse sendSmsResponse = client.sendSmsWithOptions(sendSmsRequest, new RuntimeOptions());

            // 获取响应体状态码和Body状态码
            Integer statusCode = sendSmsResponse.getStatusCode();
            String responseCode = sendSmsResponse.getBody().getCode();

            // 由于framework模块没有导入阿里云短信服务坐标-无法导入SendSmsResponse
            sendSmsResponseMap.put("statusCode", statusCode);
            sendSmsResponseMap.put("bodyCode", responseCode);
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println("诊断地址" + error.getData().get("Recommend"));
            assertAsString(error.message);
        }
        return sendSmsResponseMap;
    }


    public static Client createClient2() throws Exception {
        Config config = new Config()
                // 配置 AccessKey ID，请确保代码运行环境配置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。
                .setAccessKeyId("LTAI5tGV25U3D6ZqoPm6vd27")
                // 配置 AccessKey Secret，请确保代码运行环境配置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
                .setAccessKeySecret("******************************");
        // System.getenv()方法表示获取系统环境变量，不要直接在getenv()中填入AccessKey信息。

        // 配置 Endpoint。中国站请使用dysmsapi.aliyuncs.com
        config.endpoint = "dysmsapi.aliyuncs.com";

        return new Client(config);
    }

    public static void sendCode() {
        Client client2;
        try {
            client2 = createClient2();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        SendSmsRequest sendSmsRequest = new SendSmsRequest()
                .setPhoneNumbers("15970677836")
                .setSignName("宁波工业互联网研究院")
                .setTemplateCode("SMS_250891081")
                // TemplateParam为序列化后的JSON字符串。其中\"表示转义后的双引号。
                .setTemplateParam("123456");
        try {
            SendSmsResponse sendSmsResponse = client2.sendSms(sendSmsRequest);
            log.info(sendSmsResponse.toString());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}

