package cn.dream.common.annotation;

import java.lang.annotation.*;

/**
 * 接口日志注解
 * 
 * <AUTHOR>
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ApiLog {
    /**
     * 模块名称
     */
    String title() default "";
    
    /**
     * 是否记录请求参数
     */
    boolean requestParam() default true;
    
    /**
     * 是否记录响应结果
     */
    boolean responseResult() default true;
}
