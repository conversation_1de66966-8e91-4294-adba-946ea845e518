# Nacos 配置指南

## 问题解决
原来的 `@Value("${server.port.message}")` 配置键不存在，已修改为正确的配置键并添加了默认值。

## 当前配置说明

### application.properties 中的配置
```properties
# Spring Boot 应用配置
spring.application.name=nacos-demo
server.port=8080

# Nacos 配置中心设置
nacos.config.server-addr=127.0.0.1:8848
nacos.config.data-id=nacos-demo
nacos.config.group=DEFAULT_GROUP
nacos.config.type=properties
nacos.config.auto-refresh=true
```

### TestController 中使用的配置键
1. `${server.port:8080}` - 服务器端口，默认值 8080
2. `${app.name:默认应用名称}` - 应用名称，默认值 "默认应用名称"
3. `${test.message:默认消息}` - 测试消息，默认值 "默认消息"

## 在 Nacos 控制台中创建配置

### 步骤 1: 访问 Nacos 控制台
打开浏览器访问：http://127.0.0.1:8848/nacos
默认用户名/密码：nacos/nacos

### 步骤 2: 创建配置
1. 点击"配置管理" -> "配置列表"
2. 点击"+"按钮创建新配置
3. 填写以下信息：
   - **Data ID**: `nacos-demo`
   - **Group**: `DEFAULT_GROUP`
   - **配置格式**: `Properties`

### 步骤 3: 配置内容
在配置内容中输入：
```properties
# 服务器端口配置
server.port=9090

# 应用信息配置
app.name=我的Nacos演示应用
app.version=2.0.0
app.description=这是一个从Nacos配置中心读取配置的演示

# 测试消息配置
test.message=Hello from Nacos Config Center!

# 其他自定义配置
custom.property=这是自定义配置
database.url=**************************************
database.username=nacos_user
```

### 步骤 4: 发布配置
点击"发布"按钮保存配置。

## 测试步骤

### 1. 启动应用
```bash
mvn spring-boot:run
```

### 2. 测试接口
```bash
# 获取所有配置信息
curl http://localhost:8080/get

# 获取端口信息
curl http://localhost:8080/port

# 获取消息信息
curl http://localhost:8080/message
```

### 3. 验证配置读取
如果 Nacos 中有配置，应用会读取 Nacos 中的值；如果没有配置或连接失败，会使用代码中的默认值。

## 配置动态刷新
`nacos-config-spring-boot-starter` 支持配置自动刷新：
1. 在 Nacos 控制台修改配置
2. 点击"发布"
3. 应用会自动获取最新配置（无需重启）

## 常见问题

### 1. 启动失败 - 配置键不存在
**问题**: `@Value("${some.config}")` 中的配置键在 Nacos 中不存在
**解决**: 使用默认值语法 `@Value("${some.config:默认值}")`

### 2. 无法连接 Nacos
**问题**: 应用无法连接到 Nacos 服务器
**解决**: 
- 检查 `nacos.config.server-addr` 配置
- 确保 Nacos 服务器正在运行
- 检查网络连接

### 3. 配置不生效
**问题**: 修改 Nacos 配置后应用没有获取到新值
**解决**:
- 确保 `nacos.config.auto-refresh=true`
- 检查 Data ID 和 Group 是否匹配
- 查看应用日志是否有错误信息

## @Value 注解最佳实践

### 1. 总是提供默认值
```java
@Value("${config.key:默认值}")
private String configValue;
```

### 2. 配置键命名规范
- 使用小写字母和点号分隔：`app.name`
- 避免使用特殊字符
- 保持命名一致性

### 3. 类型转换
```java
@Value("${server.port:8080}")
private int port;  // 自动转换为 int

@Value("${app.enabled:true}")
private boolean enabled;  // 自动转换为 boolean
```
