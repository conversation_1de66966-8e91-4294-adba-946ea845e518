package cn.dream.framework.web.service;

import cn.dream.common.constant.CacheConstants;
import cn.dream.common.constant.Constants;
import cn.dream.common.constant.UserConstants;
import cn.dream.common.core.domain.entity.SysUser;
import cn.dream.common.core.domain.model.LoginUser;
import cn.dream.common.core.redis.RedisCache;
import cn.dream.common.exception.ServiceException;
import cn.dream.common.exception.user.BlackListException;
import cn.dream.common.exception.user.CaptchaException;
import cn.dream.common.exception.user.CaptchaExpireException;
import cn.dream.common.exception.user.UserNotExistsException;
import cn.dream.common.exception.user.UserPasswordNotMatchException;
import cn.dream.common.utils.DateUtils;
import cn.dream.common.utils.MessageUtils;
import cn.dream.common.utils.StringUtils;
import cn.dream.common.utils.alicloud.SmsUtils;
import cn.dream.common.utils.ip.IpUtils;
import cn.dream.framework.manager.AsyncManager;
import cn.dream.framework.manager.factory.AsyncFactory;
import cn.dream.framework.security.context.AuthenticationContextHolder;
import cn.dream.framework.security.token.SmsCodeAuthenticationToken;
import cn.dream.system.service.ISysConfigService;
import cn.dream.system.service.ISysUserService;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Component
public class SysLoginService {
    private static final Logger log = LoggerFactory.getLogger(SysLoginService.class);
    private final TokenService tokenService;

    @Resource
    private AuthenticationManager authenticationManager;

    private final RedisCache redisCache;

    private final ISysUserService userService;

    private final ISysConfigService configService;

    public SysLoginService(TokenService tokenService, RedisCache redisCache, ISysUserService userService, ISysConfigService configService) {
        this.tokenService = tokenService;
        this.redisCache = redisCache;
        this.userService = userService;
        this.configService = configService;
    }

    /**
     * 登录验证
     *
     * @param username 用户名
     * @param password 密码
     * @param code     验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    public String login(String username, String password, String code, String uuid) {
        // 验证码校验
        validateCaptcha(username, code, uuid);
        // 登录前置校验
        loginPreCheck(username, password);
        // 用户验证
        Authentication authentication = null;
        try {
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(username, password);
            AuthenticationContextHolder.setContext(authenticationToken);
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(authenticationToken);
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                throw new UserPasswordNotMatchException();
            } else {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
                throw new ServiceException(e.getMessage());
            }
        } finally {
            AuthenticationContextHolder.clearContext();
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        recordLoginInfo(loginUser.getUserId());
        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 校验验证码
     *
     * @param username 用户名
     * @param code     验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    public void validateCaptcha(String username, String code, String uuid) {
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        if (captchaEnabled) {
            String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "");
            String captcha = redisCache.getCacheObject(verifyKey);
            if (captcha == null) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire")));
                throw new CaptchaExpireException();
            }
            redisCache.deleteObject(verifyKey);
            if (!code.equalsIgnoreCase(captcha)) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
                throw new CaptchaException();
            }
        }
    }

    /**
     * 登录前置校验
     *
     * @param username 用户名
     * @param password 用户密码
     */
    public void loginPreCheck(String username, String password) {
        // 用户名或密码为空 错误
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password)) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("not.null")));
            throw new UserNotExistsException();
        }
        // 密码如果不在指定范围内 错误
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
            throw new UserPasswordNotMatchException();
        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
            throw new UserPasswordNotMatchException();
        }
        // IP黑名单校验
        String blackStr = configService.selectConfigByKey("sys.login.blackIPList");
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("login.blocked")));
            throw new BlackListException();
        }
        // 用户类型信息查询 用户类型 00:系统用户 01:手机用户 拦截非00用户
        SysUser sysUser = userService.selectUserByUserName(username);
        if (!sysUser.getUserType().equals("00")) {
            throw new UserNotExistsException();
        }
    }

    /**
     * 记录登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(Long userId) {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setLoginIp(IpUtils.getIpAddr());
        sysUser.setLoginDate(DateUtils.getNowDate());
        userService.updateUserProfile(sysUser);
    }

    public String smsLogin(String mobile, String code) {
        // 先通过mobile查询用户是否存在
        SysUser user = userService.selectUserByPhone(mobile);

        if (user == null) {
            // 1.不存在先使用手机号注册用户
            SysUser registerUser = new SysUser();
            registerUser.setPhonenumber(mobile);
            registerUser.setUserName(mobile);
            registerUser.setNickName("user" + mobile);
            //用户为手机注册用户
            registerUser.setUserType("01");
            registerUser.setStatus("0");
            registerUser.setDelFlag("0");
            userService.registerUser(registerUser);
        }

        // 2.用户存在, 用户验证
        Authentication authentication = null;
        try {
            // 使用自定义的toke鉴权器构造一个没有鉴权的token
            SmsCodeAuthenticationToken authenticationToken = new SmsCodeAuthenticationToken(mobile, code);
            AuthenticationContextHolder.setContext(authenticationToken);
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(authenticationToken);
        } catch (Exception e) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(mobile, Constants.LOGIN_FAIL, e.getMessage()));
        } finally {
            AuthenticationContextHolder.clearContext();
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(mobile, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        recordLoginInfo(loginUser.getUserId());

        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 随机生成手机验证码
     *
     * @return
     */
    public String generateSmsCode() {
        Random random = new Random();
        return String.format("%06d", random.nextInt(999999));
    }

    public Integer sendSms(String mobile) {
        String code = generateSmsCode();

        try {
            // 调用阿里云短信服务，发送短信
            Map<String, Object> sendSmsResponseMap = SmsUtils.sendMsg(mobile, code);

            // 获取响应体状态码和Body状态码
            Integer statusCode = (Integer) sendSmsResponseMap.get("statusCode");
            String bodyCode = (String) sendSmsResponseMap.get("bodyCode");

            // 判断响应码
            if (statusCode != null && statusCode.equals(200) && "OK".equals(bodyCode)) {
                // 成功发送验证码，将验证码存储在 redis 中.
                log.info("手机验证码发送成功...");
                redisCache.setCacheObject(CacheConstants.SMS_CAPTCHA_CODE_KEY + mobile, code, 300, TimeUnit.SECONDS);
                return 1;
            } else {
                return -1;
            }

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 短信验证码校验
     *
     * @param phonenumber
     * @param code
     */
    public Boolean validateSmsCode(String phonenumber, String code) {
        String smsCode = redisCache.getCacheObject(CacheConstants.SMS_CAPTCHA_CODE_KEY + phonenumber);
        return smsCode != null && smsCode.equals(code);
    }
}
