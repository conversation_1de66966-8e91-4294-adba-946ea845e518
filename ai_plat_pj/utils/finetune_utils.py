import config
from datetime import datetime
import subprocess
import json
import os
import shutil
from query_sqls.query_sqls import sqls


def start_finetuning(sql_connection):
    print("Starting finetuning...")
    finetune_sql = sqls["finetune_utils"]
    waiting_tasks = sql_connection.fetchall(finetune_sql["get_dev_status"], (config.llama_factory_config["dev_id"],))

    if waiting_tasks is None or len(waiting_tasks) == 0:
        print("No tasks found in the database. Exiting...")
        return "finished"

    exec_task = waiting_tasks[0]
    print("Starting task with id: ", exec_task["finetune_job_id"])
    model_path = config.llama_factory_config["model_path"] + "/" + str(exec_task["base_model_id"])
    dataset_info = exec_task["dataset_id"]
    # 此处不确定是否可以引入绝对路径
    output_path = config.llama_factory_config["output_path"] + "/" + str(exec_task["finetune_job_id"])
    log_path = config.llama_factory_config["finetuning_output_log_path"] + "/" + "para_log_" + str(
        exec_task["finetune_job_id"]) + ".log"
    # 更新output_path
    sql_connection.execute(finetune_sql["set_output_path"], (output_path, exec_task["finetune_job_id"]))
    sql_connection.execute(finetune_sql["set_log_path"], (log_path, exec_task["finetune_job_id"]))
    # 如果 output_path 已经存在，则删除
    if os.path.exists(output_path):
        shutil.rmtree(output_path)
    os.makedirs(output_path, exist_ok=True)

    params = json.loads(exec_task["hyperparameters"])
    params["model_name_or_path"] = model_path
    params["dataset"] = dataset_info
    params["output_dir"] = output_path
    params["dataset_dir"] = config.llama_factory_config["dataset_path"]
    params_json = json.dumps(params)
    # 构建命令
    command = [
        "bash", config.llama_factory_config["finetuning_script_path"],
        "--conda_env_name", config.llama_factory_config["conda_env_name"],
        "--params", params_json,
        "--env", "prod",  # 或者 "prod" 根据需要
        "--outputlog", config.llama_factory_config["finetuning_output_log_path"],
        "--task_id", str(exec_task["finetune_job_id"])
    ]
    sql_connection.execute(finetune_sql["set_status_running"], (exec_task["finetune_job_id"],))

    # 运行命令
    try:
        result = subprocess.run(command, check=True, text=True, capture_output=True)
        print("Command executed successfully.")
        sql_connection.execute(finetune_sql["set_status_success"], (exec_task["finetune_job_id"],))
        start_finetuning(sql_connection)
    except subprocess.CalledProcessError as e:
        print("An error occurred while executing the command.")
        print("task_id:", exec_task["finetune_job_id"])
        print("Error:", e.stderr)
        sql_connection.execute(finetune_sql["set_status_running_failed"], (exec_task["finetune_job_id"],))
        start_finetuning(sql_connection)

