sqls = {
    "llama_factory_server": {
        "dataset_init_failed": "UPDATE finetune_jobs SET status='dataset_init_failed' WHERE finetune_job_id=%s",
        "model_init_failed": "UPDATE finetune_jobs SET status='model_init_failed' WHERE finetune_job_id=%s",
        "running_failed": "UPDATE finetune_jobs SET status='running_failed' WHERE finetune_job_id=%s",
        "pending": "UPDATE finetune_jobs SET status='pending' WHERE finetune_job_id=%s",
        "running": "UPDATE finetune_jobs SET status='running' WHERE finetune_job_id=%s",
        "success": "UPDATE finetune_jobs SET status='success' WHERE finetune_job_id=%s",
        "get_finetune_jobs_info": "SELECT * FROM finetune_jobs WHERE finetune_job_id=%s",
        "get_dataset_info": "SELECT * FROM datasets WHERE dataset_id=%s",
        "get_model_info": "SELECT * FROM model_registry WHERE model_registry_id=%s",
        "get_running_dev": "SELECT * FROM finetune_jobs WHERE status = 'runnning' AND finetune_server_id=%s AND is_deleted=0 "
    },
    "model_manager_server": {
        "downloading": "UPDATE model_registry SET status='downloading' WHERE model_registry_id=%s",
        "downloaded": "UPDATE model_registry SET status='downloaded' WHERE model_registry_id=%s",
        "uploading": "UPDATE model_registry SET status='uploading' WHERE model_registry_id=%s",
        "uploaded": "UPDATE model_registry SET status='uploaded' WHERE model_registry_id=%s",
        "download_failed": "UPDATE model_registry SET status='download_failed' WHERE model_registry_id=%s",
        "upload_failed": "UPDATE model_registry SET status='upload_failed' WHERE model_registry_id=%s",
        "upload_storage_path": "UPDATE model_registry SET storage_path=%s WHERE model_registry_id=%s",
        "if_need_download": "SELECT * FROM model_registry WHERE "
                            "model_registry_id=%s "
                            "AND status IN ('downloading', 'downloaded', 'uploading', 'uploaded') "
                            "AND is_deleted=0"
    },
    "dify_server": {
        "get_parsed_documents_info": "SELECT * FROM parsed_documents WHERE document_id=%s",
        "set_document_status": "UPDATE parsed_documents SET parse_status=%s WHERE document_id=%s",
        "set_document_storage_path": "UPDATE parsed_documents SET parsed_pdf_url=%s WHERE document_id=%s",
    },
    "finetune_utils": {
        "get_dev_status": "SELECT * FROM finetune_jobs WHERE finetune_server_id=%s AND status='pending' AND is_deleted=0 ORDER BY created_at DESC",
        "set_status_running": "UPDATE finetune_jobs SET status='running' WHERE finetune_job_id=%s",
        "set_status_success": "UPDATE finetune_jobs SET status='success' WHERE finetune_job_id=%s",
        "set_status_running_failed": "UPDATE finetune_jobs SET status='running_failed' WHERE finetune_job_id=%s",
        "set_output_path": "UPDATE finetune_jobs SET output_path=%s WHERE finetune_job_id=%s",
        "set_log_path": "UPDATE finetune_jobs SET log_path=%s WHERE finetune_job_id=%s"
    },
    "gpustack_server": {
        "get_inference_info": "SELECT * FROM model_inference WHERE model_inference_id=%s",
        "update_inference_status": "UPDATE model_inference SET status=%s WHERE model_inference_id=%s",
        "update_inference_storage_path": "UPDATE model_inference SET storage_path=%s WHERE model_inference_id=%s",
        "get_inference_dev": "SELECT * FROM compute_resources WHERE type = 'inference' AND is_master!=1 AND is_deleted=0 "
    },
    "mcp_server": {
        "get_all_mcp_server": "select * from mcp_server_tool where is_deleted=0",
        "get_single_mcp_server": "SELECT * FROM mcp_server_tool where  is_deleted=0 and SERVER_NAME = %s"
    }
}
