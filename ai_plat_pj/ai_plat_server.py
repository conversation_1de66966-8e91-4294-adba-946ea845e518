from flask import Flask
import logging
import config
from utils.Mysql_utils import MysqlUtilPool
from server.llama_factory_server import llama_factory_server
from server.model_manager_server import model_manager_server
from server.gpu_stack_server import gpustack_server
from server.dify_knowledge_server import dify_server
from server.mcp_server import mcp_server
from utils.minio_utils import MinioTool

logging.basicConfig(filename='./ai_plat.log', level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')


def create_app():
    app = Flask(__name__)
    sql_connection = MysqlUtilPool()
    minio_tool = MinioTool()
    if config.ai_plat_server_config.get("llama_factory_server"):
        llama_factory_server(app, config.llama_factory_config, minio_tool, sql_connection)
    if config.ai_plat_server_config.get("model_manager_server"):
        model_manager_server(app, config.model_config, minio_tool, sql_connection)
    if config.ai_plat_server_config.get("gpustack_server"):
        gpustack_server(app, config.gpustack_config, minio_tool, sql_connection)
    if config.ai_plat_server_config.get("dify_knowledge_server"):
        dify_server(app, config.dify_server_config, minio_tool, sql_connection)
    if config.ai_plat_server_config.get("mcp_server"):
        mcp_server(app,config.mcp_server_config,minio_tool,sql_connection)
    return app


if __name__ == '__main__':
    server = create_app()
    # 测试环境运行端口 5000
    server.run(host='0.0.0.0', port=5000, debug=True)
    # LX开发环境运行端口 5002
    # server.run(host='0.0.0.0', port=5002, debug=True)
