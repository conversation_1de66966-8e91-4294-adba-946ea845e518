import asyncio
import json
import logging
import os
import shutil
import threading
from enum import Enum
from typing import Dict, Optional, Any, List
from concurrent.futures import ThreadPoolExecutor
from fastapi import FastAPI
from fastapi.openapi.utils import get_openapi
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from mcp.client.sse import sse_client
from mcp.client.streamable_http import streamablehttp_client
from dataclasses import dataclass

from mcpserver.openapidoc import  get_model_fields, get_tool_handler
from query_sqls.query_sqls import sqls
from utils.Mysql_utils import MysqlUtilPool

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
mcp_server_sql = sqls['mcp_server']

@dataclass
class ServerConfiguration:
    """启动/连接一个mcp服务器需要的配置"""
    name:str
    config:dict[str,Any]

class ServerStatus(Enum):
    """
    服务启动的状态
    """
    STARTING = "STARTING"
    STARTED = "STARTED"
    FAILED = "FAILED"

class ConfigurationProvider:
    def get_configuration(self,server_name) -> ServerConfiguration:
        pass
    def get_all_configurations(self) -> List[ServerConfiguration]:
        pass
class MySqlConfigurationProvider(ConfigurationProvider):
    def __init__(self,sql_connection:MysqlUtilPool) -> None:
        self.sql_connection = sql_connection
    """从 mysql中获取server配置"""
    def get_configuration(self,server_name) -> ServerConfiguration:
        server = self.sql_connection.fetchone(mcp_server_sql['get_single_mcp_server'],server_name)
        server_config = ServerConfiguration(server['server_name'],json.loads(server['params']))
        return server_config
    def get_all_configurations(self) -> List[ServerConfiguration]:
        server_list = self.sql_connection.fetchall(mcp_server_sql['get_all_mcp_server'])
        return [ServerConfiguration(server['server_name'],json.loads(server['params'])) for server in server_list]

class JsonConfigurationProvider(ConfigurationProvider):
    """
    从 json中根据server名称获取配置
    """
    def get_configuration(self, server_name) -> ServerConfiguration:
        server_config = JsonFileReader.load_config('mcpserver/servers_config.json')
        config = server_config['mcpServers'][server_name]
        return ServerConfiguration(server_name,config)

    def get_all_configurations(self) -> List[ServerConfiguration]:
        result = []
        all_server_config = JsonFileReader.load_config('mcpserver/servers_config.json')['mcpServers']
        for server_name,config in  all_server_config.items():
            result.append(ServerConfiguration(server_name,config))
        return result


class JsonFileReader:
    @staticmethod
    def load_config(file_path: str) -> Dict[str, Any]:
        with open(file_path, 'r') as f:
            return json.load(f)

class BasicServer:
    def __init__(self, name: str, config: Dict[str, Any]) -> None:
        self.status = ServerStatus.STARTING
        self.name: str = name
        self.config: Dict[str, Any] = config
        self.session: Optional[ClientSession] = None
        self.capabilities: Optional[Dict[str, Any]] = None
        self.tools = {}
        self.openapi = {}
        # 每个server 创建mcp连接和调用tool需要保持在同一个event loop的上下文
        self.event_loop = asyncio.new_event_loop()

    """ 初始化 server 连接"""
    async def initialize(self) -> None:
        pass

    async def list_tools_map(self) -> dict[str,Any]:
        """
            返回 tool的dict
        Raises:
            RuntimeError: If the server is not initialized.
        """
        if len(self.tools) !=0:
            return self.tools
        tools_response = await self.session.list_tools()

        supports_progress = (
                self.capabilities
                and 'progress' in self.capabilities
        )

        if supports_progress:
            logging.info(f"Server {self.name} supports progress tracking")

        for item in tools_response:
            if isinstance(item, tuple) and item[0] == 'tools':
                for tool in item[1]:
                    self.tools[tool.name] =Tool(tool.name, tool.description, tool.inputSchema)
                    if supports_progress:
                        logging.info(f"Tool '{tool.name}' will support progress tracking")
        return self.tools

    async def execute_tool(
            self,
            tool_name: str,
            arguments: Dict[str, Any],
            retries: int = 2,
            delay: float = 1.0
    ) -> Any:
        """Execute a tool with retry mechanism.

        Args:
            tool_name: Name of the tool to execute.
            arguments: Tool arguments.
            retries: Number of retry attempts.
            delay: Delay between retries in seconds.

        Returns:
            Tool execution result.

        Raises:
            RuntimeError: If server is not initialized.
            Exception: If tool execution fails after all retries.
        """
        if not self.session:
            raise RuntimeError(f"Server {self.name} not initialized")

        attempt = 0
        while attempt < retries:
            try:
                supports_progress = (
                        self.capabilities
                        and 'progress' in self.capabilities
                )

                if supports_progress:
                    logging.info(f"Executing {tool_name} with progress tracking...")
                    result = await self.session.call_tool(
                        tool_name,
                        arguments
                    )
                else:
                    logging.info(f"Executing {tool_name}...")
                    result = await self.session.call_tool(tool_name, arguments)
                return result
            except Exception as e:
                attempt += 1
                logging.warning(f"Error executing tool: {e}. Attempt {attempt} of {retries}.")
                if attempt < retries:
                    logging.info(f"Retrying in {delay} seconds...")
                    await asyncio.sleep(delay)
                else:
                    logging.error("Max retries reached. Failing.")
                    raise
    async def cleanup(self) -> None:
        """Clean up server resources."""
        try:
            if self.session:
                try:
                    await self.session.__aexit__(None, None, None)
                except Exception as e:
                    logging.warning(f"Warning during session cleanup for {self.name}: {e}")
                finally:
                    self.session = None
        except Exception as e:
            logging.error(f"Error during cleanup of server {self.name}: {e}")


class StdioServer(BasicServer):
    """Manages MCP server connections and tool execution."""

    def __init__(self, name: str, config: Dict[str, Any]) -> None:
        super().__init__(name, config)
        self.stdio_client: Optional[Any] = None

    async def initialize(self) -> None:
        """Initialize the server connection."""
        server_params = StdioServerParameters(
            command=shutil.which("npx") if self.config['command'] == "npx" else self.config['command'],
            args=self.config['args'],
            env={**os.environ, **self.config['env']} if self.config.get('env') else None
        )
        try:
            self.stdio_client = stdio_client(server_params)
            read, write = await self.stdio_client.__aenter__()
            self.session = ClientSession(read, write)
            await self.session.__aenter__()
            self.capabilities = await self.session.initialize()
        except Exception as e:
            logging.error(f"Error initializing server {self.name}: {e}")
            await self.cleanup()
        finally:
            self.status = ServerStatus.FAILED


    async def cleanup(self) -> None:
        """Clean up server resources."""
        await super().cleanup()
        if self.stdio_client:
            try:
                await self.stdio_client.__aexit__(None, None, None)
                print('关闭stdio成功')
            except (RuntimeError, asyncio.CancelledError) as e:
                logging.info(f"Note: Normal shutdown message for {self.name}: {e}")
            except Exception as e:
                logging.warning(f"Warning during stdio cleanup for {self.name}: {e}")
            finally:
                self.stdio_client = None
class SSEServer(BasicServer):
    def __init__(self, name: str, config: Dict[str, Any]) -> None:
        super().__init__(name, config)
        self.sse_client: Optional[Any] = None

    async def initialize(self) -> None:
        """Initialize the server connection."""
        headers = self.config['headers'] if 'headers' in self.config else {}
        url = self.config['sse'] if 'sse' in self.config else None
        try:
            self.sse_client =  sse_client(url=url,headers=headers)
            self.session =  ClientSession(*await self.sse_client.__aenter__())
            self.session = await self.session.__aenter__()
            self.capabilities = await self.session.initialize()
        except Exception as e:
            logging.error(f"Error initializing server {self.name}: {e}")
            await self.cleanup()
        finally:
            self.status = ServerStatus.FAILED

    async def cleanup(self) -> None:
        await super().cleanup()
        if self.sse_client:
            try:
                await self.sse_client.__aexit__(None, None, None)
            except (RuntimeError, asyncio.CancelledError) as e:
                logging.info(f"Note: Normal shutdown message for {self.name}: {e}")
            except Exception as e:
                logging.warning(f"Warning during stdio cleanup for {self.name}: {e}")
            finally:
                self.sse_client = None

class StreamableServer(BasicServer):
    def __init__(self, name: str, config: Dict[str, Any]) -> None:
        super().__init__(name, config)
        self.streamable_client: Optional[Any] = None

    async def initialize(self) -> None:
        """Initialize the server connection."""
        headers = self.config['headers'] if 'headers' in self.config else {}
        url = self.config['mcp'] if 'mcp' in self.config else None
        try:
            self.streamable_client =  streamablehttp_client(url=url,headers=headers)
            #只需要前两个参数，mcp源码有问题
            streams = await self.streamable_client.__aenter__()
            self.session =  ClientSession(streams[0],streams[1])
            self.session = await self.session.__aenter__()
            self.capabilities = await self.session.initialize()
        except Exception as e:
            logging.error(f"Error initializing server {self.name}: {e}")
            await self.cleanup()
        finally:
            self.status = ServerStatus.FAILED
    async def cleanup(self) -> None:
        await super().cleanup()
        if self.streamable_client:
            try:
                await self.streamable_client.__aexit__(None, None, None)
            except (RuntimeError, asyncio.CancelledError) as e:
                logging.info(f"Note: Normal shutdown message for {self.name}: {e}")
            except Exception as e:
                logging.warning(f"Warning during stdio cleanup for {self.name}: {e}")
            finally:
                self.streamable_client = None

class Tool:
    """Represents a tool with its properties and formatting."""

    def __init__(self, name: str, description: str, input_schema: Dict[str, Any]) -> None:
        self.name: str = name
        self.description: str = description
        self.input_schema: Dict[str, Any] = input_schema

    def __str__(self):
        return f'Tool {self.name} description: {self.description}'


class McpServerManager:
    """Orchestrates the interaction between user, LLM, and tools."""

    def __init__(self) -> None:
        self.servers: dict[str,BasicServer] = {}
        self.all_tools=[]
        self.mcp_server_config ={}
        self.pool:ThreadPoolExecutor|None = None

    async def cleanup_servers(self) -> None:
        """Clean up all servers properly."""
        cleanup_tasks = []
        for name, server in self.servers.items():
                cleanup_tasks.append(asyncio.create_task(server.cleanup()))
        if cleanup_tasks:
            try:
                await asyncio.gather(*cleanup_tasks, return_exceptions=True)
            except Exception as e:
                logging.warning(f"Warning during final cleanup: {e}")

    def stop_server(self,name) -> None:
        if name in self.servers:
            server = self.servers[name]
            server.event_loop.run_until_complete(self.servers[name].cleanup())
            #移除
            del self.servers[name]

    def start_servers(self, configs : List[ServerConfiguration]):
        """启动多个server"""
        for config in configs:
            self.start_server(config)

    def start_server(self, server_config:ServerConfiguration):
        """ 新增一个server """
        config = server_config.config
        server_name = server_config.name
        print(f"启动server:{server_name}, config:{config}")
        if server_name in self.servers:
            print(f"重复启动任务:{server_name}")
            return
        if 'sse' in config:
            server = SSEServer(server_name, config)
        elif 'mcp' in config:
            server = StreamableServer(server_name, config)
        else:
            server = StdioServer(server_name, config)
        self.servers[server_name]=server
        server.event_loop.run_until_complete(server.initialize())
        tool_dict = server.event_loop.run_until_complete(server.list_tools_map())

        server.status = ServerStatus.STARTED
        #生成服务器openapi文档信息
        def openapi():
            openapi_dict = get_server_openapi_schema(server,tool_dict)
            print(f'server:{server_name}, openapi:{openapi_dict}')
            #从配置里面读取当前server信息
            openapi_dict['servers'] = self.mcp_server_config['server_urls']
            #转成dify可用的json形式
            server.openapi = json.dumps(openapi_dict,ensure_ascii=False)
        #异步生成文档
        self.pool.submit(openapi)
        #启动成功
        print(f'启动{server_name}成功')

    def call_tool(self,serverName:str,toolName:str, arguments: dict) -> str:
        try:
                server = self.servers[serverName]
                tools_dict =  server.event_loop.run_until_complete(server.list_tools_map())
                if toolName in tools_dict:
                    try:
                        result = server.event_loop.run_until_complete(server.execute_tool(toolName, arguments))
                        return result
                    except Exception as e:
                        error_msg = f"Error executing tool: {str(e)}"
                        logging.error(error_msg)
                        return error_msg
                return f"No server found with tool: {toolName}"
        finally:pass

def get_server_openapi_schema(server:BasicServer,tools_dict)->Dict[str,Any]:
    """
     生成server的openapi文档， 这个openApi文档是不包含 servers的信息的
    :param server:
    :return:
    """
    main_app = FastAPI()
    for _,tool in tools_dict.items():
        endpoint_name = tool.name
        endpoint_description = tool.description
        input_schema = tool.input_schema
        output_schema = getattr(tool, "output_schema", None)

        form_model_fields = get_model_fields(
            f"{endpoint_name}_form_model",
            input_schema.get("properties", {}),
            input_schema.get("required", []),
            input_schema.get("$defs", {}),
        )
        response_model_fields = None
        if output_schema:
            response_model_fields = get_model_fields(
                f"{endpoint_name}_response_model",
                output_schema.get("properties", {}),
                output_schema.get("required", []),
                output_schema.get("$defs", {}),
            )
        try:
            tool_handler = get_tool_handler(
                endpoint_name,
                form_model_fields,
                response_model_fields,
            )
            main_app.post(
                f"/mcp/call/{server.name}/{endpoint_name}",
                summary=endpoint_name.replace("_", " ").title(),
                description=endpoint_description,
                response_model_exclude_none=True,
                dependencies= [],
            )(tool_handler)
        except Exception as e:
            logging.error(f"Warning during tool handler {tool.name}: {e}")
    openapi_schema = get_openapi(
        title=server.name,
        version="1.0.0",
        description="mcpserver",
        routes=main_app.routes,
    )
    return openapi_schema