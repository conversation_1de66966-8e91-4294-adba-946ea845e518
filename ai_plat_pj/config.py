ai_plat_server_config = {
    "llama_factory_server": <PERSON>als<PERSON>,
    "gpustack_server": <PERSON>als<PERSON>,
    "model_manager_server": False,
    "dify_knowledge_server": False,
    "mcp_server": True,
}

database_config = {
    'host': '*************',
    'user': 'root',
    'password': '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@1701%Test2',
    'database': 'ry-cloud-ai',
    'port': 3317,
    'charset': 'utf8',
}
# 测试环境配置
llama_factory_config = {
    "path": "/data/llama_fac_home_20250226/LLaMA-Factory",
    "dataset_path": "/data/llama_fac_home_20250226/LLaMA-Factory/data",
    "model_path": "/data/llama_fac_home_20250226/LLaMA-Factory/models",
    "model_bucket": "models-bucket",
    "dataset_bucket": "datasets",
    "output_path": "/data/finetune_output_model",
    "export_dir": "/data/export_model",
    "finetuning_script_path": "/data/llama_fac_home_20250226/ai_plat_pj/utils/finetuning_utils.sh",
    "finetuning_export_script_path": "/data/llama_fac_home_20250226/ai_plat_pj/utils/finetuning_export_utils.sh",
    "finetuning_output_log_path": "/data/llama_fac_home_20250226/ai_plat_pj/utils/finetuning_param_log",
    "conda_env_name": "llama_fac_20250226",
    "dev_id": 2,
}

minio_config = {
    "endpoint": "*************:9000",
    "access_key": "minioadmin",
    "secret_key": "minioadmin123",
    "secure": False,
}
dify_server_config = {
    "document_input_path": "/data/document_parsed/document_input_dir",
    "document_output_path": "/data/document_parsed/document_output_dir",
    "parsed_script_path": "/data/llama_fac_home_20250226/ai_plat_pj/utils/document_parsed_utils.sh",
    "document_download_bucket": "original-document",
    "document_upload_bucket":"parsed-document",
    "pic_upload_bucket":"mineruimg",
    "mineru_conda_env": "mineru",
    "parsed_log_path": "/data/llama_fac_home_20250226/ai_plat_pj/utils/document_parsed_log",
    "minio_server_url": "http://*************:9000",
}
gpustack_config = {
    "script_path": "/data/llama_fac_home_20250226/ai_plat_pj/utils/inference_utils.sh",
    "conda_env_name": "convert_script",
    "model_path": "/data/inference/gguf_input",
    "output_path": "/data/inference/gguf_output",
    "outputlog": "/data/llama_fac_home_20250226/ai_plat_pj/gguf_log",
    "convert-script-path": "/data/llama_fac_home_20250226/ai_plat_pj/llama.cpp/convert_hf_to_gguf.py ",
    "model_bucket": "models-bucket",

}

model_config = {
    "path": "/data/model/public",
    "model_bucket": "models-bucket",
}

mcp_server_config = {
    # sql 或者 json
    "config_provider":"json",
    "server_urls":[{"url": "http://***********:5000/", "description": "生产环境服务器"}],
}
