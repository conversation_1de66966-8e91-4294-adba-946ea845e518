{"name": "@wonderwhy-er/desktop-commander", "version": "0.2.1", "description": "MCP server for terminal operations and file editing", "license": "MIT", "author": "<PERSON><PERSON>", "homepage": "https://github.com/wonderwhy-er/DesktopCommanderMCP", "bugs": "https://github.com/wonderwhy-er/DesktopCommanderMCP/issues", "type": "module", "engines": {"node": ">=18.0.0"}, "bin": {"desktop-commander": "dist/index.js", "setup": "dist/setup-claude-server.js"}, "files": ["dist", "logo.png", "testemonials"], "scripts": {"sync-version": "node scripts/sync-version.js", "bump": "node scripts/sync-version.js --bump", "bump:minor": "node scripts/sync-version.js --bump --minor", "bump:major": "node scripts/sync-version.js --bump --major", "build": "tsc && shx cp setup-claude-server.js dist/ && shx chmod +x dist/*.js", "watch": "tsc --watch", "start": "node dist/index.js", "start:debug": "node --inspect-brk=9229 dist/index.js", "setup": "npm install && npm run build && node setup-claude-server.js", "setup:debug": "npm install && npm run build && node setup-claude-server.js --debug", "prepare": "npm run build", "test": "node test/run-all-tests.js", "link:local": "npm run build && npm link", "unlink:local": "npm unlink", "inspector": "npx @modelcontextprotocol/inspector dist/index.js", "logs:view": "npm run build && node scripts/view-fuzzy-logs.js", "logs:analyze": "npm run build && node scripts/analyze-fuzzy-logs.js", "logs:clear": "npm run build && node scripts/clear-fuzzy-logs.js", "logs:export": "npm run build && node scripts/export-fuzzy-logs.js"}, "publishConfig": {"access": "public"}, "keywords": ["mcp", "model-context-protocol", "terminal", "claude", "ai", "command-line", "process-management", "file-editing", "code-editing", "diff", "patch", "block-editing", "file-system", "text-manipulation", "code-modification", "surgical-edits", "file-operations"], "dependencies": {"@modelcontextprotocol/sdk": "^1.8.0", "@vscode/ripgrep": "^1.15.9", "cross-fetch": "^4.1.0", "fastest-levenshtein": "^1.0.16", "glob": "^10.3.10", "zod": "^3.24.1", "zod-to-json-schema": "^3.23.5"}, "devDependencies": {"@types/node": "^20.17.24", "commander": "^13.1.0", "nexe": "^5.0.0-beta.4", "nodemon": "^3.0.2", "shx": "^0.3.4", "typescript": "^5.3.3"}}