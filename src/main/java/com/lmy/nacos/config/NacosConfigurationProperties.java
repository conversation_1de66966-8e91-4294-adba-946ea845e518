package com.lmy.nacos.config;

import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 使用 Nacos 配置属性类
 * 这个类会自动从 Nacos 配置中心读取配置
 */
@Component
@NacosConfigurationProperties(dataId = "nacos-demo", groupId = "DEFAULT_GROUP", autoRefreshed = true)
public class NacosConfigurationProperties {
    
    private String message = "默认消息";
    private String name = "默认应用名称";
    private String version = "1.0.0";
    
    // Getter and Setter methods
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getVersion() {
        return version;
    }
    
    public void setVersion(String version) {
        this.version = version;
    }
    
    @Override
    public String toString() {
        return "NacosConfigurationProperties{" +
                "message='" + message + '\'' +
                ", name='" + name + '\'' +
                ", version='" + version + '\'' +
                '}';
    }
}
