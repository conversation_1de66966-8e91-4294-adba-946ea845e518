package com.lmy.nacos.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * Nacos配置属性类
 * 用于演示从Nacos配置中心获取配置
 */
@Component
@RefreshScope
@ConfigurationProperties(prefix = "app")
public class NacosConfig {
    
    private String name = "默认应用名称";
    private String version = "1.0.0";
    private String description = "这是一个Nacos配置演示应用";
    
    // Getter and Setter methods
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getVersion() {
        return version;
    }
    
    public void setVersion(String version) {
        this.version = version;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    @Override
    public String toString() {
        return "NacosConfig{" +
                "name='" + name + '\'' +
                ", version='" + version + '\'' +
                ", description='" + description + '\'' +
                '}';
    }
}
