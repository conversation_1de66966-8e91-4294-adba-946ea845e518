package com.lmy.nacos.config;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MapPropertySource;

import javax.annotation.PostConstruct;
import java.util.Properties;

@Configuration
public class NacosConfig {

    @Value("${nacos.config.server-addr}")
    private String serverAddr;

    @Value("${nacos.config.data-id}")
    private String dataId;

    @Value("${nacos.config.group}")
    private String group;

    private final ConfigurableEnvironment environment;

    public NacosConfig(ConfigurableEnvironment environment) {
        this.environment = environment;
    }

    @PostConstruct
    public void loadNacosConfig() {
        try {
            Properties props = new Properties();
            props.put("serverAddr", serverAddr);

            ConfigService configService = NacosFactory.createConfigService(props);
            String config = configService.getConfig(dataId, group, 5000);

            if (config != null && !config.trim().isEmpty()) {
                System.out.println("从 Nacos 加载的配置内容：");
                System.out.println(config);

                // 解析配置并添加到 Spring Environment
                Properties nacosProperties = new Properties();
                String[] lines = config.split("\n");
                for (String line : lines) {
                    line = line.trim();
                    if (!line.isEmpty() && !line.startsWith("#")) {
                        String[] keyValue = line.split("=", 2);
                        if (keyValue.length == 2) {
                            nacosProperties.setProperty(keyValue[0].trim(), keyValue[1].trim());
                        }
                    }
                }

                // 将 Nacos 配置添加到 Spring Environment
                MapPropertySource nacosPropertySource = new MapPropertySource("nacos", nacosProperties);
                environment.getPropertySources().addFirst(nacosPropertySource);

                System.out.println("成功加载 Nacos 配置到 Spring Environment");
            } else {
                System.out.println("警告：从 Nacos 获取的配置为空");
            }
        } catch (NacosException e) {
            System.err.println("连接 Nacos 失败：" + e.getMessage());
            e.printStackTrace();
        }
    }

    @Bean
    public ConfigService configService() throws NacosException {
        Properties props = new Properties();
        props.put("serverAddr", serverAddr);
        return NacosFactory.createConfigService(props);
    }
}
