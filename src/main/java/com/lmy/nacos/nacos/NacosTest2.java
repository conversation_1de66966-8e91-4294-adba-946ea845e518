//package com.lmy.nacos.nacos;
//
//import org.springframework.context.annotation.AnnotationConfigApplicationContext;
//
//public class NacosTest2 {
//
//    public static void main(String[] args) {
//        AnnotationConfigApplicationContext context = new AnnotationConfigApplicationContext(MyConfig.class);
//        ServerConfig serverConfig = context.getBean(ServerConfig.class);
//        serverConfig.getPort();
//
//    }
//}
