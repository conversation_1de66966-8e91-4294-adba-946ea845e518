package com.lmy.nacos.nacos;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.config.listener.Listener;
import com.alibaba.nacos.api.exception.NacosException;

import java.io.IOException;
import java.util.Properties;
import java.util.concurrent.Executor;

public class NacosTest {

    public static void main(String[] args) {
        String config = null;
        try {
            String serverAddr="127.0.0.1:8848";

            String dataId="test0001";
            String group = "DEFAULT_GROUP";

            Properties props = new Properties();
            props.put("serverAddr", serverAddr);

            ConfigService configService = NacosFactory.createConfigService(props);
            config = configService.getConfig(dataId, group, 50000);
            System.out.println(config);
            configService.addListener(dataId,group, new Listener() {
                @Override
                public Executor getExecutor() {
                    return null;
                }

                @Override
                public void receiveConfigInfo(String s) {
                    System.out.println("receiveConfigInfo");
                    System.out.println(s);
                }
            });

            System.in.read();
        } catch (NacosException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }


    }
}
