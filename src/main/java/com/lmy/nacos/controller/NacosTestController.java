package com.lmy.nacos.controller;

import com.lmy.nacos.config.NacosConfigurationProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/nacos-test")
public class NacosTestController {

    @Autowired
    private NacosConfigurationProperties nacosConfig;

    @GetMapping("/config")
    public Map<String, Object> getNacosConfig() {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", nacosConfig.getMessage());
        result.put("name", nacosConfig.getName());
        result.put("version", nacosConfig.getVersion());
        result.put("source", "从 @NacosConfigurationProperties 读取");
        return result;
    }
    
    @GetMapping("/message")
    public String getMessage() {
        return "Nacos Message: " + nacosConfig.getMessage();
    }
    
    @GetMapping("/name")
    public String getName() {
        return "Nacos App Name: " + nacosConfig.getName();
    }
}
