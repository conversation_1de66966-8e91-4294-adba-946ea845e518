package com.lmy.nacos.controller;

import com.alibaba.nacos.api.config.ConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/config-test")
public class ConfigTestController {

    @Autowired
    private Environment environment;

    @Autowired
    private ConfigService configService;

    @GetMapping("/nacos-direct")
    public Map<String, Object> getNacosConfigDirect() {
        Map<String, Object> result = new HashMap<>();
        try {
            String config = configService.getConfig("nacos-demo", "DEFAULT_GROUP", 5000);
            result.put("success", true);
            result.put("config", config);
            result.put("message", "直接从 Nacos 获取配置成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("message", "直接从 Nacos 获取配置失败");
        }
        return result;
    }

    @GetMapping("/spring-env")
    public Map<String, Object> getSpringEnvironment() {
        Map<String, Object> result = new HashMap<>();
        result.put("server.port.message", environment.getProperty("server.port.message"));
        result.put("test.message", environment.getProperty("test.message"));
        result.put("server.port", environment.getProperty("server.port"));
        result.put("nacos.config.server-addr", environment.getProperty("nacos.config.server-addr"));
        return result;
    }

    @GetMapping("/check-connection")
    public Map<String, Object> checkNacosConnection() {
        Map<String, Object> result = new HashMap<>();
        try {
            // 测试连接
            String config = configService.getConfig("nacos-demo", "DEFAULT_GROUP", 3000);
            result.put("connected", true);
            result.put("configExists", config != null && !config.trim().isEmpty());
            result.put("configContent", config);
            result.put("message", "Nacos 连接正常");
        } catch (Exception e) {
            result.put("connected", false);
            result.put("error", e.getMessage());
            result.put("message", "Nacos 连接失败");
        }
        return result;
    }
}
