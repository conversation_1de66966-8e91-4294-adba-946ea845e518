package com.lmy.nacos.controller;

import com.lmy.nacos.config.NacosConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 配置演示Controller
 */
@RestController
@RequestMapping("/config")
public class ConfigController {
    
    @Autowired
    private NacosConfig nacosConfig;
    
    @GetMapping("/info")
    public Map<String, Object> getConfigInfo() {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("data", nacosConfig);
        result.put("message", "成功获取Nacos配置信息");
        return result;
    }
    
    @GetMapping("/app-name")
    public String getAppName() {
        return nacosConfig.getName();
    }
    
    @GetMapping("/app-version")
    public String getAppVersion() {
        return nacosConfig.getVersion();
    }
    
    @GetMapping("/app-description")
    public String getAppDescription() {
        return nacosConfig.getDescription();
    }
}
