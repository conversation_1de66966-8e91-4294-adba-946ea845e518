package com.lmy.nacos.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RefreshScope  // 支持配置动态刷新
public class TestController {

    @Value("${server.port:8080}")
    private String port;

    @Value("${test.message:默认消息}")
    private String message;

    @GetMapping("/get")
    public String get() {
        return "Server Port: " + port + ", Message: " + message;
    }

    @GetMapping("/port")
    public String getPort() {
        return port;
    }

    @GetMapping("/message")
    public String getMessage() {
        return message;
    }
}
