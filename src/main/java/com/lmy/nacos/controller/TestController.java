package com.lmy.nacos.controller;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class TestController {

    // 从 Nacos 配置中心读取配置，如果没有则使用默认值
    // 根据您的测试结果，Nacos 中有这些配置：server.port.message=8088, test.message=1111

    @NacosValue(value = "${server.port.message}",autoRefreshed = true)
    private String serverPortMessage;

    @Value("${test.message}")
    private String testMessage;

    @Value("${server.port}")
    private String serverPort;

    @GetMapping("/get")
    public String get() {
        return "Server Port Message: " + serverPortMessage + ", Test Message: " + testMessage + ", Server Port: " + serverPort;
    }

    @GetMapping("/port-message")
    public String getPortMessage() {
        return "Server Port Message: " + serverPortMessage;
    }

    @GetMapping("/message")
    public String getMessage() {
        return "Test Message: " + testMessage;
    }

    @GetMapping("/all-config")
    public String getAllConfig() {
        StringBuilder sb = new StringBuilder();
        sb.append("=== 从 Nacos 读取的配置 ===").append("\n");
        sb.append("server.port.message: ").append(serverPortMessage).append("\n");
        sb.append("test.message: ").append(testMessage).append("\n");
        sb.append("server.port: ").append(serverPort).append("\n");
        return sb.toString();
    }

}
