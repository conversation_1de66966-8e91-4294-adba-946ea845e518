package com.lmy.nacos.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class TestController {

    // 从 Nacos 配置中心读取配置，如果没有则使用默认值
    @Value("${server.port:8080}")
    private String port;

    @Value("${app.name:默认应用名称}")
    private String appName;

    @Value("${app.version:1.0.0}")
    private String appVersion;

    @Value("${app.description:默认描述}")
    private String appDescription;

    @Value("${test.message:默认消息}")
    private String testMessage;

    @Value("${database.url:********************************}")
    private String databaseUrl;

    @Value("${database.username:root}")
    private String databaseUsername;

    @GetMapping("/get")
    public String get() {
        return "Server Port: " + port + ", Message: " + testMessage;
    }

    @GetMapping("/port")
    public String getPort() {
        return "Server Port: " + port;
    }

    @GetMapping("/app-info")
    public String getAppInfo() {
        return "App Name: " + appName + ", Version: " + appVersion + ", Description: " + appDescription;
    }

    @GetMapping("/message")
    public String getMessage() {
        return "Test Message: " + testMessage;
    }

    @GetMapping("/database")
    public String getDatabaseInfo() {
        return "Database URL: " + databaseUrl + ", Username: " + databaseUsername;
    }

    @GetMapping("/all-config")
    public String getAllConfig() {
        StringBuilder sb = new StringBuilder();
        sb.append("=== Nacos 配置信息 ===").append("\n");
        sb.append("Server Port: ").append(port).append("\n");
        sb.append("App Name: ").append(appName).append("\n");
        sb.append("App Version: ").append(appVersion).append("\n");
        sb.append("App Description: ").append(appDescription).append("\n");
        sb.append("Test Message: ").append(testMessage).append("\n");
        sb.append("Database URL: ").append(databaseUrl).append("\n");
        sb.append("Database Username: ").append(databaseUsername).append("\n");
        return sb.toString();
    }
}
