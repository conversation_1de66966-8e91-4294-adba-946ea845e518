package com.lmy.nacos.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/diagnostic")
public class DiagnosticController {

    @Autowired
    private Environment environment;

    @GetMapping("/config")
    public Map<String, Object> getConfigInfo() {
        Map<String, Object> result = new HashMap<>();
        
        // 检查 Nacos 相关配置
        result.put("nacos.config.server-addr", environment.getProperty("nacos.config.server-addr"));
        result.put("nacos.config.data-id", environment.getProperty("nacos.config.data-id"));
        result.put("nacos.config.group", environment.getProperty("nacos.config.group"));
        result.put("nacos.config.type", environment.getProperty("nacos.config.type"));
        result.put("nacos.config.auto-refresh", environment.getProperty("nacos.config.auto-refresh"));
        
        // 检查应用配置
        result.put("spring.application.name", environment.getProperty("spring.application.name"));
        result.put("server.port", environment.getProperty("server.port"));
        
        // 检查我们要读取的配置
        result.put("test.message", environment.getProperty("test.message"));
        result.put("app.name", environment.getProperty("app.name"));
        
        return result;
    }
    
    @GetMapping("/properties")
    public Map<String, String> getAllProperties() {
        Map<String, String> result = new HashMap<>();
        
        // 获取所有以 test. 开头的属性
        String[] propertyNames = {
            "test.message",
            "app.name", 
            "app.version",
            "server.port",
            "nacos.config.server-addr",
            "nacos.config.data-id"
        };
        
        for (String propertyName : propertyNames) {
            String value = environment.getProperty(propertyName);
            result.put(propertyName, value != null ? value : "NOT_FOUND");
        }
        
        return result;
    }
}
