package com.lmy.langChain4j.assistant;

import dev.langchain4j.service.MemoryId;
import dev.langchain4j.service.SystemMessage;
import dev.langchain4j.service.UserMessage;
import dev.langchain4j.service.spring.AiService;
import dev.langchain4j.service.spring.AiServiceWiringMode;

@AiService(wiringMode = AiServiceWiringMode.EXPLICIT,
        chatModel = "qwenChatModel",
        chatMemoryProvider = "chatMemoryProvider")
public interface Assistant3Proxy {

    @SystemMessage("你是一个东北人，并且是我的好朋友，今天是{{current_date}}")
    String chat(@MemoryId String memoryId, @UserMessage String userMessage);
}
