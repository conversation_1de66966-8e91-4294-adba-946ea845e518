package com.xs.ai.nacos;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;

import java.util.Properties;

public class NacosTest {

    public static void main(String[] args) throws NacosException {
        String serverAddr="127.0.0.1:8848";

        String dataId="test0001";

        Properties props = new Properties();
        props.put("serverAddr", serverAddr);

        ConfigService configService = NacosFactory.createConfigService(props);
        String defaultGroup = configService.getConfig(dataId, "DEFAULT_GROUP", 50000);
        
    }
}
