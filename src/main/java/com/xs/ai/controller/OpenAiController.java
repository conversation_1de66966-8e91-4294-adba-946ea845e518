package com.xs.ai.controller;

import com.alibaba.cloud.ai.dashscope.chat.DashScopeChatModel;
import com.xs.ai.services.BookingTools;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.AbstractChatMemoryAdvisor;
import org.springframework.ai.chat.client.advisor.PromptChatMemoryAdvisor;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.ollama.OllamaChatModel;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

import java.time.LocalDate;


/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 */
@RestController
@CrossOrigin
public class OpenAiController {

    /**
     * 百炼千问
     */
    ChatClient client;
    /**
     * 本地ollama deepseek1.5
     */

    public OpenAiController(DashScopeChatModel chatModel,
                            ChatMemory inMemoryChatMemory,
                            BookingTools bookingTools) {
        this.client = ChatClient.builder(chatModel)
                .defaultAdvisors(new PromptChatMemoryAdvisor(inMemoryChatMemory))
                .defaultSystem("""
                       您是“Tuling”航空公司的客户聊天支持代理。请以友好、乐于助人且愉快的方式来回复。
                        您正在通过在线聊天系统与客户互动。
                        在提供有关预订或取消预订的信息之前，您必须始终
                        从用户处获取以下信息：预订号、客户姓名。
                        在询问用户之前，请检查消息历史记录以获取此信息。
                        在更改或退订之前，请先获取预订信息并且用户确定之后才进行更改或退订。
                        请讲中文。
                        今天的日期是 {current_date}.
                       """)
                .defaultTools(bookingTools)
                .build();
    }


    @CrossOrigin
    @GetMapping(value = "/ai/generateStreamAsString", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> generateStreamAsString(@RequestParam(value = "message") String message) {
        return client.prompt().user(message)
                .system(s -> s.param("current_date", LocalDate.now().toString()))
                .advisors(a -> a.param(AbstractChatMemoryAdvisor.CHAT_MEMORY_RETRIEVE_SIZE_KEY, 100))
                .stream()
                .content()
                .concatWith(Flux.just("[complete]"));

    }


}
