package com.xs.ai.services;

import cn.idev.excel.EasyExcel;
import com.xs.ai.data.BoxInfoAnalysis;
import com.xs.ai.listener.BatchImportListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;

@Slf4j
@Service
public class ExcelTest {


    public List<String> analysisExcel(MultipartFile multipartFile) {
        if (multipartFile == null || multipartFile.isEmpty()) {
            log.error("解析Excel失败：文件为空");
        }
        try {
            // 创建监听器
            BatchImportListener listener = new BatchImportListener();
            // 清空监听器中的数据
            listener.clear();
            File file = convertMultipartFileToFile(multipartFile);
            // 使用EasyExcel解析Excel文件


            try {
                EasyExcel.read(file, BoxInfoAnalysis.class, listener).sheet().doRead();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }


            // 获取解析到的ID列表
            List<String> ids = listener.getAllIds();

            log.info("成功解析Excel文件，共获取{}\u4e2aID", ids.size());
            return ids;

        } catch (Exception e) {
            log.error("解析Excel文件失败: {}", e.getMessage(), e);
        }
        return null;
    }

    private File convertMultipartFileToFile(MultipartFile multipartFile) throws IOException {
        File file = File.createTempFile("temp", null);
        try (FileOutputStream fos = new FileOutputStream(file)) {
            fos.write(multipartFile.getBytes());
        }
        return file;
    }
}
