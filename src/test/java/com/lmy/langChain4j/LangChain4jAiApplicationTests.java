package com.lmy.langChain4j;

import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.ollama.OllamaChatModel;
import dev.langchain4j.model.openai.OpenAiChatModel;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class LangChain4jAiApplicationTests {

  @Resource
  private OpenAiChatModel openAiChatModel;

  @Resource
  private ChatLanguageModel chatLanguageModel;


  @Resource
  private OllamaChatModel ollamaChatModel;

    @Test
    public void test1(){
        OpenAiChatModel model = OpenAiChatModel.builder()
                .baseUrl("http://langchain4j.dev/demo/openai/v1")
                .apiKey("demo")
                .modelName("gpt-4o-mini")
                .build();

        String chat = model.chat("你好，今天的日期是几号");
        System.out.println(chat);
    }

    @Test
    public void test2(){
        String chat = openAiChatModel.chat("你好，你是谁");
        System.out.println(chat);
    }
    @Test
    public void test3(){
        String chat = chatLanguageModel.chat("你好，你是谁,你能干什么");
        System.out.println(chat);
    }

    @Test
    public void test4(){
        String chat = ollamaChatModel.chat("你好，你是谁");
        System.out.println(chat);

    }

}
