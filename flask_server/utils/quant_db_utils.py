import configparser
import logging
import os
import traceback
import sys
from dbutils.pooled_db import PooledDB
import pymysql

logging.basicConfig(filename='app.log', level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')


class MysqlUtilPool():
    def __init__(self, config):
        db_config = config['database']
        self.POOL = PooledDB(
            creator=pymysql,  # 使用链接数据库的模块
            maxconnections=20,  # 连接池允许的最大连接数，0和None表示不限制连接数
            mincached=2,  # 初始化时，链接池中至少创建的空闲的链接，0表示不创建
            maxcached=10,  # 链接池中最多闲置的链接，0和None不限制
            blocking=True,  # 连接池中如果没有可用连接后，是否阻塞等待。True，等待；False，不等待然后报错
            maxusage=None,  # 一个链接最多被重复使用的次数，None表示无限制
            host=db_config.get('host'),
            user=db_config.get('user'),
            password=db_config.get('password'),
            database=db_config.get('database'),
            port=int(db_config.get('port')),
            charset='utf8'
        )

    def _get_connection(self):
        return self.POOL.connection()

    def execute(self, sql, params=None, fetchone=False, fetchall=False):
        conn = self._get_connection()
        cursor = conn.cursor(cursor=pymysql.cursors.DictCursor)
        try:
            if params:
                cursor.execute(sql, params)  # 使用参数执行SQL
            else:
                cursor.execute(sql)
            if fetchone:
                result = cursor.fetchone()
            elif fetchall:
                result = cursor.fetchall()
            else:
                conn.commit()
                return None
            if result is None or len(result) == 0:
                return None
            return result
        except Exception as e:
            logging.info("发生异常:", e)
            logging.info("SQL语句错误:", sql)
            traceback.print_exc()
            conn.rollback()
        finally:
            cursor.close()
            conn.close()

    def executemany(self, sql, values):
        conn = self._get_connection()
        cursor = conn.cursor(cursor=pymysql.cursors.DictCursor)
        try:
            cursor.executemany(sql, values)
            conn.commit()
            return cursor.rowcount
        except Exception as e:
            logging.info("发生异常:", e)
            logging.info("SQL语句错误:", sql)
            traceback.print_exc()
            conn.rollback()
        finally:
            cursor.close()
            conn.close()

    def insert(self, sql):
        self.execute(sql)

    def fetchone(self, sql, params=None):
        return self.execute(sql, fetchone=True, params=params)

    def fetchall(self, sql, params=None):
        return self.execute(sql, fetchall=True, params=params)

    def delete(self, sql):
        self.execute(sql)

    def update(self, sql):
        self.execute(sql)
