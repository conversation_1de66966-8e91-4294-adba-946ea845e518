import inspect
from service.datasets_service import DatasetsService
from utils.http_utils import *
from flask import request
from form.datasets import Dataset


class DatasetsController:
    def __init__(self, app, sql_connection):
        self.base_path = '/datasets'
        self.app = app
        self.sql_connection = sql_connection
        self.service = DatasetsService(self.sql_connection)
        self._register_routes()

    def _register_routes(self):
        # 获取当前类的所有方法
        members = inspect.getmembers(self, predicate=inspect.ismethod)

        # 遍历所有方法，查找以 "api_" 开头的方法并注册为路由
        for name, method in members:
            if name.startswith("api_"):  # 约定：以 "api_" 开头的方法是对外开放的接口
                route_end = f"/{name[4:]}"
                print(route_end)
                route_path = self.base_path + route_end  # 去掉 "api_" 前缀作为路由路径
                endpoint = name  # 使用方法名作为 endpoint
                # 获取允许的 HTTP 方法
                methods = getattr(method, 'methods', ['GET'])  # 默认允许 GET
                self.app.add_url_rule(route_path, endpoint, method, methods=methods)

    @http_method(['GET'])
    def api_get_datasets(self):
        """
        获取所有数据集列表
        """
        try:
            datasets = self.service.selectAll()
            return make_response(success=True, data=datasets, status_code=200)
        except Exception as e:
            # 记录日志（可选）
            return make_response(success=False, message=str(e), status_code=500)

    @http_method(['GET'])
    def api_get_dataset_by_id(self):
        """
        根据数据集 ID 获取数据集详情
        """
        dataset_id = request.args.get('id')
        try:
            dataset = self.service.selectById(dataset_id)
            return make_response(success=True, data=dataset, status_code=200)
        except Exception as e:
            # 记录日志（可选）
            return make_response(success=False, message=str(e), status_code=500)

    @http_method(['POST'])
    def api_create_dataset(self):
        """
        创建数据集
        """
        print(request.json)
        dataset = Dataset(**request.json)
        print(dataset)
        try:
            dataset_id = self.service.insert(dataset)
            return make_response(success=True, data=dataset_id, status_code=200)
        except Exception as e:
            # 记录日志（可选）
            return make_response(success=False, message=str(e), status_code=500)

    @http_method(['POST'])
    def api_update_dataset(self):
        """
        更新数据集
        """
        dataset = Dataset(**request.json)
        try:
            self.service.update(dataset)
            return make_response(success=True, status_code=200)
        except Exception as e:
            # 记录日志（可选）
            return make_response(success=False, message=str(e), status_code=500)

    @http_method(['GET'])
    def api_delete_dataset(self):
        """
        删除数据集
        """
        dataset_id = request.args.get('id')
        try:
            self.service.delete(dataset_id)
            return make_response(success=True, status_code=200)
        except Exception as e:
            # 记录日志（可选）
            return make_response(success=False, message=str(e), status_code=500)
