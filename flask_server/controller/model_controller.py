import inspect
import os

from flask import request
from modelscope.hub.snapshot_download import snapshot_download

from service.model_service import ModelService
from utils.http_utils import make_response, http_method


class ModelController:
    """
    模型相关接口控制器
    """

    def __init__(self, app, sql_connection):
        self.base_path = '/model'
        self.app = app
        self.sql_connection = sql_connection
        self._register_routes()

    def _register_routes(self):
        # 获取当前类的所有方法
        members = inspect.getmembers(self, predicate=inspect.ismethod)

        # 遍历所有方法，查找以 "api_" 开头的方法并注册为路由
        for name, method in members:
            if name.startswith("api_"):  # 约定：以 "api_" 开头的方法是对外开放的接口
                route_end = f"/{name[4:]}"
                route_path = self.base_path + route_end  # 去掉 "api_" 前缀作为路由路径
                endpoint = f"model_{name}"  # 使用方法名作为 endpoint
                # 获取允许的 HTTP 方法
                methods = getattr(method, 'methods', ['GET'])  # 默认允许 GET
                self.app.add_url_rule(route_path, endpoint, method, methods=methods)

    def api_list(self):
        """
        获取模型列表接口
        请求参数：
        - page: 页码，默认1
        - page_size: 每页数量，默认10
        - model_name: 模型名称（用于筛选），可选
        """
        try:
            # 获取请求参数
            page = request.args.get('page', default=1, type=int)
            page_size = request.args.get('page_size', default=10, type=int)
            model_name = request.args.get('model_name', default="", type=str)

            # 参数验证
            if page < 1:
                page = 1
            if page_size < 1 or page_size > 100:
                page_size = 10

            # 调用服务层获取数据
            service = ModelService(self.sql_connection)
            models, total = service.get_models(page, page_size, model_name)

            # 转换为字典列表
            model_list = []
            for model in models:
                model_dict = {
                    "model_id": model.model_id,
                    "model_name": model.model_name,
                    "path": model.path
                }
                model_list.append(model_dict)

            # 返回结果
            return make_response(success=True, data={
                "list": model_list,
                "total": total,
                "page": page,
                "page_size": page_size
            }, status_code=200)
        except Exception as e:
            # 记录错误日志
            import logging
            logging.error(f"获取模型列表异常: {str(e)}")

            # 返回错误信息
            return make_response(success=False, message=f"获取模型列表失败: {str(e)}", status_code=500)

    @http_method(['POST'])
    def api_download(self):
        """
        模型下载接口
        请求参数：
        - path: 模型路径
        - model_name: 模型名称
        """
        try:
            # 获取请求参数
            data = request.get_json()
            path = data.get('path', '')
            model_name = data.get('model_name', '')

            # 参数验证
            if not path or not model_name:
                return make_response(success=False, message="参数错误：path和model_name不能为空", status_code=400)

            # 拼接下载名称
            download_name = f"{path}/{model_name}"

            # 设置缓存目录，这里使用项目目录上一级下的models文件夹
            cache_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
                                     'models')
            os.makedirs(cache_dir, exist_ok=True)
            print(f"缓存目录：{cache_dir}")

            # 调用ModelScope SDK下载模型
            try:
                model_dir = snapshot_download(download_name, cache_dir=cache_dir)
                return make_response(success=True, data={"model_dir": model_dir}, message="模型下载成功",
                                     status_code=200)
            except Exception as e:
                return make_response(success=False, message=f"模型下载失败: {str(e)}", status_code=500)

        except Exception as e:
            # 记录错误日志
            import logging
            logging.error(f"模型下载异常: {str(e)}")

            # 返回错误信息
            return make_response(success=False, message=f"模型下载失败: {str(e)}", status_code=500)
