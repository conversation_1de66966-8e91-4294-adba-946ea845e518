package cn.dream.bean.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/5 上午11:12
 */
@Data
public class RetrieverResource {
    private String position;
    @JsonProperty("dataset_id")
    private String datasetId;
    @JsonProperty("dataset_name")
    private String datasetName;
    @JsonProperty("document_id")
    private String documentId;
    @JsonProperty("document_name")
    private String documentName;
    @JsonProperty("data_source_type")
    private String dataSourceType;
    @JsonProperty("segment_id")
    private String segmentId;
    @JsonProperty("retriever_from")
    private String retrieverFrom;
    private String score;
    @JsonProperty("hit_count")
    private int hitCount;
    @JsonProperty("word_count")
    private int wordCount;
    @JsonProperty("segment_position")
    private int segmentPosition;
    @JsonProperty("index_node_hash")
    private String indexNodeHash;
    private String content;


}
