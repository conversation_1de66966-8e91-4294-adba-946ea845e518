package cn.dream.tools.ai;

import cn.dream.client.AiApi;
import cn.dream.common.utils.StringUtils;
import cn.dream.config.ChatGPTProperties;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import retrofit2.Retrofit;

import java.net.InetSocketAddress;
import java.net.Proxy;
import java.time.Duration;
import java.util.Random;

/**
 * &#064;Author:  liudong
 * &#064;Date:  2024/7/6 14:00
 * &#064;Description:  AiProxyService
 *
 * <AUTHOR>
 */
@Slf4j
public class AiProxyService extends AiService {

    private final Random random = new Random();

    private final ChatGPTProperties chatGptProperties;

    public AiProxyService(ChatGPTProperties chatGPTProperties) {
        this(chatGPTProperties, Duration.ofSeconds(600), AiProxyService.defaultClient(chatGPTProperties, Duration.ofSeconds(600)));
    }

    public AiProxyService(ChatGPTProperties chatGptProperties, OkHttpClient okHttpClient) {
        this(chatGptProperties, Duration.ofSeconds(600), okHttpClient);
    }

    public AiProxyService(ChatGPTProperties chatGptProperties, Duration timeout, OkHttpClient client) {
        super(buildApi(chatGptProperties, timeout, client), chatGptProperties.getBaseUrl());
        this.chatGptProperties = chatGptProperties;

    }

    public static AiApi buildApi(ChatGPTProperties properties, Duration timeout, OkHttpClient okHttpClient) {
        ObjectMapper mapper = defaultObjectMapper();
        OkHttpClient client = okHttpClient == null ? defaultClient(properties, timeout) : okHttpClient;
        Retrofit retrofit = defaultRetrofit(client, mapper, properties.getBaseUrl());
        return retrofit.create(AiApi.class);
    }

    public static OkHttpClient defaultClient(ChatGPTProperties properties, Duration timeout) {
        if (!StringUtils.hasText(properties.getProxyHost())) {
            return AiService.defaultClient(properties, timeout);
        }
        // Create proxy object
        Proxy proxy = new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(properties.getProxyHost(), properties.getProxyPort()));
        return AiService.defaultClient(properties, timeout).newBuilder()
                .proxy(proxy)
                .build();
    }
}
