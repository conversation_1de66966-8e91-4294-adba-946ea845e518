# Nacos 配置读取故障排除指南

## 问题现象
访问 `http://localhost:8080/message` 返回"默认消息"，而不是 Nacos 中配置的值。

## 可能的原因和解决方案

### 1. 检查 Nacos 服务器状态
**检查步骤：**
```bash
# 检查 Nacos 是否运行
curl http://127.0.0.1:8848/nacos/v1/console/health/readiness

# 或者访问 Nacos 控制台
# http://127.0.0.1:8848/nacos
```

**解决方案：**
如果 Nacos 没有运行，请启动 Nacos 服务器。

### 2. 检查 Nacos 中的配置
**检查步骤：**
1. 访问 Nacos 控制台：http://127.0.0.1:8848/nacos
2. 登录（默认用户名/密码：nacos/nacos）
3. 进入"配置管理" -> "配置列表"
4. 查找 Data ID 为 `nacos-demo` 的配置

**正确的配置应该是：**
- **Data ID**: `nacos-demo`
- **Group**: `DEFAULT_GROUP`
- **配置格式**: `Properties`
- **配置内容**:
```properties
test.message=Hello from Nacos!
app.name=我的Nacos应用
app.version=2.0.0
```

### 3. 使用诊断接口检查配置
启动应用后，访问以下接口来诊断问题：

```bash
# 检查配置信息
curl http://localhost:8080/diagnostic/config

# 检查所有属性
curl http://localhost:8080/diagnostic/properties

# 测试 Nacos 配置属性类
curl http://localhost:8080/nacos-test/config
```

### 4. 检查应用日志
查看应用启动日志，寻找 Nacos 相关的错误信息：

**正常情况下应该看到：**
```
[INFO] Loading nacos data, dataId: 'nacos-demo', group: 'DEFAULT_GROUP'
[INFO] Nacos config loaded successfully
```

**异常情况可能看到：**
```
[ERROR] Failed to connect to Nacos server
[WARN] No configuration found for dataId: nacos-demo
```

### 5. 常见配置问题

#### 问题 A: Data ID 不匹配
**现象**: 应用读取不到配置
**原因**: application.properties 中的 `nacos.config.data-id` 与 Nacos 中的 Data ID 不一致
**解决**: 确保两者完全一致

#### 问题 B: Group 不匹配
**现象**: 应用读取不到配置
**原因**: Group 配置不一致
**解决**: 确保 application.properties 中的 `nacos.config.group` 与 Nacos 中的 Group 一致

#### 问题 C: 配置格式错误
**现象**: 配置读取失败
**原因**: Nacos 中配置格式与 `nacos.config.type` 不匹配
**解决**: 确保配置格式一致（Properties 或 YAML）

### 6. 网络连接问题
**检查步骤：**
```bash
# 测试网络连接
telnet 127.0.0.1 8848

# 或者使用 curl
curl -I http://127.0.0.1:8848
```

### 7. 依赖版本兼容性
对于 Spring Boot 3.x，确保使用正确的依赖版本：

```xml
<dependency>
    <groupId>com.alibaba.boot</groupId>
    <artifactId>nacos-config-spring-boot-starter</artifactId>
    <version>0.2.12</version>
</dependency>
<dependency>
    <groupId>com.alibaba.nacos</groupId>
    <artifactId>nacos-client</artifactId>
    <version>2.2.4</version>
</dependency>
```

## 测试步骤

### 步骤 1: 重新编译和启动
```bash
mvn clean compile
mvn spring-boot:run
```

### 步骤 2: 测试诊断接口
```bash
# 检查配置状态
curl http://localhost:8080/diagnostic/config

# 检查 Nacos 配置属性
curl http://localhost:8080/nacos-test/message
```

### 步骤 3: 测试原始接口
```bash
# 测试 @Value 注解
curl http://localhost:8080/message

# 测试完整信息
curl http://localhost:8080/get
```

## 预期结果

**如果配置正确，应该看到：**
- `/diagnostic/config` 返回 Nacos 配置信息
- `/nacos-test/message` 返回 Nacos 中配置的消息
- `/message` 返回 Nacos 中配置的消息（而不是默认值）

## 进一步调试

如果问题仍然存在，请：

1. **启用详细日志**：
   在 application.properties 中添加：
   ```properties
   logging.level.com.alibaba.nacos=DEBUG
   logging.level.com.lmy.nacos=DEBUG
   ```

2. **检查 Nacos 服务器日志**：
   查看 Nacos 服务器的日志文件，确认客户端连接情况

3. **使用 Nacos 客户端直接测试**：
   创建一个简单的 Java 程序直接连接 Nacos 获取配置

## 最终验证

成功配置后，您应该能够：
1. 在 Nacos 控制台修改配置
2. 应用自动获取新配置（无需重启）
3. 接口返回 Nacos 中的实际配置值
