# Nacos配置中心演示项目

## 项目说明
这是一个演示如何使用Spring Cloud Alibaba Nacos作为配置中心的项目。

## 修复的问题
1. **依赖版本兼容性**：升级到与Spring Boot 3.x兼容的Spring Cloud Alibaba版本
2. **配置方式**：使用标准的Spring Cloud配置方式替代旧的Nacos注解
3. **配置文件结构**：使用bootstrap.yml和application.yml的标准配置结构

## 项目结构
```
src/
├── main/
│   ├── java/
│   │   └── com/lmy/nacos/
│   │       ├── config/
│   │       │   └── NacosConfig.java          # 配置属性类
│   │       ├── controller/
│   │       │   ├── TestController.java       # 测试控制器
│   │       │   └── ConfigController.java     # 配置演示控制器
│   │       └── NacosApplication.java         # 主应用类
│   └── resources/
│       ├── bootstrap.yml                     # 启动配置
│       ├── application.yml                   # 应用配置
│       └── application.properties            # 属性配置
```

## 使用步骤

### 1. 启动Nacos服务器
确保Nacos服务器在 `127.0.0.1:8848` 上运行。

### 2. 在Nacos中创建配置
在Nacos控制台中创建以下配置：

**Data ID**: `nacos-demo-dev.yml`
**Group**: `DEFAULT_GROUP`
**配置格式**: `YAML`
**配置内容**:
```yaml
# 服务器配置
server:
  port: 8080

# 测试消息配置
test:
  message: "这是从Nacos配置中心获取的消息"

# 应用配置
app:
  name: "Nacos演示应用"
  version: "2.0.0"
  description: "这是一个从Nacos配置中心获取配置的演示应用"
```

### 3. 启动应用
```bash
mvn spring-boot:run
```

### 4. 测试接口

#### 基本测试接口
- `GET /get` - 获取端口和消息信息
- `GET /port` - 获取服务器端口
- `GET /message` - 获取测试消息

#### 配置信息接口
- `GET /config/info` - 获取完整配置信息
- `GET /config/app-name` - 获取应用名称
- `GET /config/app-version` - 获取应用版本
- `GET /config/app-description` - 获取应用描述

### 5. 测试配置动态刷新
1. 在Nacos控制台修改配置
2. 调用Spring Boot Actuator的刷新端点：
   ```bash
   curl -X POST http://localhost:8080/actuator/refresh
   ```
3. 重新访问接口查看配置是否更新

## 配置说明

### bootstrap.yml
- 配置Nacos服务器地址
- 配置应用名称和环境
- 配置文件扩展名和分组

### application.yml
- 配置管理端点
- 配置日志级别

## 注意事项
1. 确保Nacos服务器正常运行
2. 配置的Data ID格式为：`{spring.application.name}-{spring.profiles.active}.{file-extension}`
3. 使用`@RefreshScope`注解支持配置动态刷新
4. 使用`@Value`注解获取简单配置值
5. 使用`@ConfigurationProperties`注解获取复杂配置对象

## 常见问题解决
1. **无法连接Nacos服务器**：检查服务器地址和端口
2. **配置获取失败**：检查Data ID、Group和Namespace是否正确
3. **配置不刷新**：确保使用了`@RefreshScope`注解并调用了刷新端点
