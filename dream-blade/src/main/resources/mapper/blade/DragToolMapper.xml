<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dream.blade.mapper.DragToolMapper">

    <resultMap type="cn.dream.blade.domain.entity.DragTool" id="DragToolResult">
        <id property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="inputText" column="input_text"/>
        <result property="ratio" column="ratio"/>
        <result property="layout" column="layout"/>
        <result property="width" column="width"/>
        <result property="height" column="height"/>
        <result property="userId" column="user_id"/>
        <result property="picUrl" column="pic_url"/>
        <result property="localPic" column="local_pic"/>
        <result property="text" column="text"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectDragToolVo">
        select id,
               type,
               input_text,
               ratio,
               layout,
               width,
               height,
               user_id,
               pic_url,
               local_pic,
               text,
               create_time,
               update_time
        from drag_tool
    </sql>


    <select id="selectDragToolListByUserId" parameterType="Integer" resultMap="DragToolResult">
        <include refid="selectDragToolVo"/>
        where user_id = #{userId}
         <if test="type != null and type != ''">
             and  type = #{type}
         </if>
         and is_delete = 0
        ORDER BY create_time DESC
    </select>

    <insert id="insertDragTool" parameterType="cn.dream.blade.domain.entity.DragTool" useGeneratedKeys="true" keyProperty="id">
        insert into drag_tool
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="inputText != null and inputText != ''">input_text,</if>
            <if test="type != null">type,</if>
            <if test="ratio != null and ratio != ''">ratio,</if>
            <if test="layout != null and layout != ''">layout,</if>
            <if test="width != null">width,</if>
            <if test="height != null">height,</if>
            <if test="userId != null">user_id,</if>
            <if test="picUrl != null and picUrl != ''">pic_url,</if>
            <if test="localPic != null and localPic != ''">local_pic,</if>
            <if test="text != null and text != ''">text,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="inputText != null and inputText != ''">#{inputText},</if>
            <if test="type != null">#{type},</if>
            <if test="ratio != null and ratio != ''">#{ratio},</if>
            <if test="layout != null and layout != ''">#{layout},</if>
            <if test="width != null">#{width},</if>
            <if test="height != null">#{height},</if>
            <if test="userId != null">#{userId},</if>
            <if test="picUrl != null and picUrl != ''">#{picUrl},</if>
            <if test="localPic != null and localPic != ''">#{localPic},</if>
            <if test="text != null and text != ''">#{text},</if>
        </trim>
    </insert>

    <delete id="deleteById">
        update drag_tool set is_delete = 1 where id = #{id}
    </delete>

</mapper>
