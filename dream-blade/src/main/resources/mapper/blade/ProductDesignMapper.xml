<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dream.blade.mapper.ProductDesignMapper">

    <resultMap type="cn.dream.blade.domain.ProductDesign" id="ProductDesignResult">
        <id property="id" column="id"/>
        <id property="inputImageUrl" column="input_image_url"/>
        <id property="inputText" column="input_text"/>
        <result property="productName" column="product_name"/>
        <result property="packageType" column="package_type"/>
        <result property="designStyle" column="design_style"/>
        <result property="designElement" column="design_element"/>
        <result property="material" column="material"/>
        <result property="color" column="color"/>
        <result property="jsonData" column="json_data"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="userName" column="user_name"/>
        <result property="userId" column="user_id"/>
    </resultMap>

    <sql id="selectProductDesignVo">
        select id,
               input_image_url,
               input_text,
               product_name,
               package_type,
               design_style,
               design_element,
               material,
               color,
               json_data,
               user_name,
               user_id,
               create_by,
               create_time,
               update_by,
               update_time,
               remark
        from blade_product_design
    </sql>

    <select id="selectProductDesignList" parameterType="cn.dream.blade.domain.ProductDesign"
            resultMap="ProductDesignResult">
        <include refid="selectProductDesignVo"/>
        <where>
            <if test="productName != null and productName != ''">
                and (
                product_name like concat('%', #{productName}, '%') or
                package_type like concat('%', #{productName}, '%') or
                design_style like concat('%', #{productName}, '%') or
                design_element like concat('%', #{productName}, '%') or
                material like concat('%', #{productName}, '%') or
                color like concat('%', #{productName}, '%')
                )
            </if>
            <if test="packageType != null and packageType != '' and productName == null">and package_type like
                concat('%', #{packageType}, '%')
            </if>
            <if test="designStyle != null and designStyle != '' and productName == null">and design_style like
                concat('%', #{designStyle}, '%')
            </if>
            <if test="designElement != null and designElement != '' and productName == null">and design_element like
                concat('%', #{designElement}, '%')
            </if>
            <if test="material != null and material != '' and productName == null">and material like concat('%',
                #{material}, '%')
            </if>
            <if test="color != null and color != '' and productName == null">and color like concat('%', #{color}, '%')
            </if>
        </where>
    </select>
</mapper>
