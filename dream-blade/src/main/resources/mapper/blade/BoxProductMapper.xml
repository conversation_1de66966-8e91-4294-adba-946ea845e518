<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dream.blade.mapper.BoxProductMapper">

    <resultMap type="cn.dream.blade.domain.BoxProduct" id="BoxProductResult">
        <id property="id" column="id"/>
        <result property="recordId" column="record_id"/>
        <result property="productName" column="product_name"/>
        <result property="boxId" column="box_id"/>
        <result property="boxImg" column="box_img"/>
        <result property="type" column="type"/>
        <result property="userId" column="user_id"/>
        <result property="width" column="width"/>
        <result property="height" column="height"/>
        <result property="length" column="length"/>
        <result property="materialName" column="material_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>


    <sql id="selectBoxProductVo">
        select id,
               record_id,
               product_name,
               box_id,
               box_img,
               type,
               user_id,
               width,
               height,
               length,
               material_name,
               create_time,
               update_time,
               is_delete
        from box_product
    </sql>


    <insert id="insertBySelective" parameterType="cn.dream.blade.domain.BoxProduct">
        insert into box_product (record_id, product_name, box_id, box_img, type, user_id, width, height, length,
                                 material_name)
        values (#{product.recordId}, #{product.productName}, #{product.boxId}, #{product.boxImg}, #{product.type},
                #{product.userId}, #{product.width}, #{product.height}, #{product.length}, #{product.materialName})
    </insert>

    <insert id="insertBatchSomeColumn" parameterType="java.util.List">
        insert into box_product (record_id,product_name,box_id,box_img,type,user_id,width,height,length,material_name)
        values
        <foreach item="item" index="index" collection="products" separator=",">
            (#{item.recordId},#{item.productName},#{item.boxId},#{item.boxImg},#{item.type},#{item.userId},#{item.width},#{item.height},#{item.length},#{item.materialName})
        </foreach>
    </insert>

    <select id="selectByParams" resultMap="BoxProductResult" resultType="java.util.List">
        <include refid="selectBoxProductVo"/>
        <where>
            <if  test="product.recordIds != null and product.recordIds != ''">
                and record_id in
                <foreach collection="product.recordIds" item="recordId" open="(" separator="," close=")">
                    #{recordId}
                </foreach>
            </if>
            <if test="product.productName != null and product.productName != ''">
                and product_name like concat('%', #{product.productName}, '%')
            </if>
            <if test="product.userId != null and product.userId != ''">
                and user_id = #{product.userId}
            </if>
            and is_delete = 0
        </where>
    </select>





    <delete id="deleteById">
        update box_product set is_delete = 1 where id = #{id}
    </delete>

</mapper>
