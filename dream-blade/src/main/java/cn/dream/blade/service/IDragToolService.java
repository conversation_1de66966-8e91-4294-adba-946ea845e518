package cn.dream.blade.service;

import cn.dream.blade.domain.entity.DragTool;
import cn.dream.blade.domain.request.DragToolType;

import java.util.List;

/**
 * 图像指令Service接口
 *
 * <AUTHOR>
 */
public interface IDragToolService {


    /**
     * 根据用户ID查询图像指令列表
     *
     * @return 图像指令集合
     */
    List<DragTool> selectImageCommandListByUserId(DragToolType type);

    /**
     * 新增图像指令
     *
     * @param dragTool 图像指令
     * @return 结果
     */
    int insertImageCommand(DragTool dragTool);

    /**
     * 删除图像指令
     *
     * @param id 图像指令ID
     * @return 结果
     */
    void deleteById(Integer id);
}
