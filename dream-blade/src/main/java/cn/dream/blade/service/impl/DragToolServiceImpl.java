package cn.dream.blade.service.impl;

import cn.dream.blade.domain.entity.ImageCommand;
import cn.dream.blade.mapper.ImageCommandMapper;
import cn.dream.blade.service.IImageCommandService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 图像指令Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ImageCommandServiceImpl implements IImageCommandService {

    @Resource
    private ImageCommandMapper imageCommandMapper;


    /**
     * 根据用户ID查询图像指令列表
     *
     * @param userId 用户ID
     * @return 图像指令集合
     */
    @Override
    public List<ImageCommand> selectImageCommandListByUserId(Long userId,Integer type) {
        return imageCommandMapper.selectImageCommandListByUserId(userId);
    }

    /**
     * 新增图像指令
     *
     * @param imageCommand 图像指令
     * @return 结果
     */
    @Override
    public int insertImageCommand(ImageCommand imageCommand) {
        return imageCommandMapper.insertImageCommand(imageCommand);
    }

}
