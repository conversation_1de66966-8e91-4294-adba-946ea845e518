package cn.dream.blade.service;

import cn.dream.blade.domain.BoxInfo;

import java.util.List;

/**
 * 盒子信息Service接口
 *
 * <AUTHOR>
 */
public interface IBoxInfoService
{
    /**
     * 查询盒子信息
     *
     * @param id 盒子信息主键
     * @return 盒子信息
     */
    public BoxInfo selectBoxInfoById(String id);

    /**
     * 查询盒子信息列表
     *
     * @param boxInfo 盒子信息
     * @return 盒子信息集合
     */
    public List<BoxInfo> selectBoxInfoList(BoxInfo boxInfo);

    /**
     * 新增盒子信息
     *
     * @param boxInfo 盒子信息
     * @return 结果
     */
    public int insertBoxInfo(BoxInfo boxInfo);

    /**
     * 修改盒子信息
     *
     * @param boxInfo 盒子信息
     * @return 结果
     */
    public int updateBoxInfo(BoxInfo boxInfo);

    /**
     * 批量删除盒子信息
     *
     * @param ids 需要删除的盒子信息主键集合
     * @return 结果
     */
    public int deleteBoxInfoByIds(String[] ids);

    /**
     * 删除盒子信息信息
     *
     * @param id 盒子信息主键
     * @return 结果
     */
    public int deleteBoxInfoById(String id);
}
