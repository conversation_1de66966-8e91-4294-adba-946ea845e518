package cn.dream.blade.service;

import cn.dream.blade.domain.request.EditProductRequest;
import cn.dream.blade.domain.request.GetProductListRequest;
import cn.dream.blade.domain.request.AddProductRequest;
import cn.dream.blade.domain.request.AddRecommendProductRequest;
import cn.dream.blade.domain.vo.BoxProductListVO;

import java.util.List;

/**
 * 盒子产品Service接口
 * 
 * <AUTHOR>
 */
public interface IBoxProductService  {

    String batchAddRecommend(List<AddRecommendProductRequest> addRecommendProductRequests);

    List<BoxProductListVO> getProductList(GetProductListRequest request);

    void deleteProduct(String id);

    void addProduct(AddProductRequest addProductRequest);

    void editProduct(EditProductRequest editProductRequest);
}
