package cn.dream.blade.service;

import java.util.List;
import cn.dream.blade.domain.ProductDesign;
import cn.dream.blade.domain.dto.ProductDesignDTO;
import cn.dream.blade.domain.dto.ProductDesignQueryDTO;

/**
 * 产品设计信息Service接口
 *
 * <AUTHOR>
 */
public interface IProductDesignService
{

    /**
     * 使用查询DTO查询产品设计信息列表
     *
     * @param queryDTO 查询DTO
     * @return 产品设计信息集合
     */
     List<ProductDesign> selectProductDesignList(ProductDesignQueryDTO queryDTO);

    /**
     * 从DTO新增产品设计信息
     *
     * @param productDesignDTO 产品设计信息DTO
     * @return 结果
     */
     int insertProductDesignFromDTO(ProductDesignDTO productDesignDTO);


}
