package cn.dream.blade.service.impl;

import cn.dream.blade.domain.ProductDesign;
import cn.dream.blade.dto.ProductDesignDTO;
import cn.dream.blade.dto.ProductDesignQueryDTO;
import cn.dream.blade.mapper.ProductDesignMapper;
import cn.dream.blade.service.IProductDesignService;
import cn.dream.common.exception.ServiceException;
import cn.dream.common.utils.StringUtils;
import cn.dream.common.utils.uuid.UUID;
import com.alibaba.fastjson2.JSON;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 产品设计信息Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class ProductDesignServiceImpl implements IProductDesignService
{
    @Autowired
    private ProductDesignMapper productDesignMapper;

    /**
     * 查询产品设计信息
     *
     * @param id 产品设计信息主键
     * @return 产品设计信息
     */
    @Override
    public ProductDesign selectProductDesignById(String id)
    {
        return productDesignMapper.selectProductDesignById(id);
    }

    /**
     * 查询产品设计信息列表
     *
     * @param productDesign 产品设计信息
     * @return 产品设计信息
     */
    @Override
    public List<ProductDesign> selectProductDesignList(ProductDesign productDesign)
    {
        return productDesignMapper.selectProductDesignList(productDesign);
    }

    /**
     * 使用查询DTO查询产品设计信息列表
     *
     * @param queryDTO 查询DTO
     * @return 产品设计信息集合
     */
    @Override
    public List<ProductDesign> selectProductDesignList(ProductDesignQueryDTO queryDTO)
    {
        ProductDesign productDesign = new ProductDesign();

        // 如果关键词不为空，则设置到各个字段中进行模糊查询
        if (StringUtils.isNotEmpty(queryDTO.getKeyword()))
        {
            String keyword = queryDTO.getKeyword();
            productDesign.setProductName(keyword);
            productDesign.setPackageType(keyword);
            productDesign.setDesignStyle(keyword);
            productDesign.setDesignElement(keyword);
            productDesign.setMaterial(keyword);
            productDesign.setColor(keyword);
        }

        return productDesignMapper.selectProductDesignList(productDesign);
    }

    /**
     * 新增产品设计信息
     *
     * @param productDesign 产品设计信息
     * @return 结果
     */
    @Override
    public int insertProductDesign(ProductDesign productDesign)
    {
        return productDesignMapper.insertProductDesign(productDesign);
    }

    /**
     * 从DTO新增产品设计信息
     *
     * @param productDesignDTO 产品设计信息DTO
     * @return 结果
     */
    @Override
    public int insertProductDesignFromDTO(ProductDesignDTO productDesignDTO)
    {
        // 创建产品设计对象
        ProductDesign productDesign = new ProductDesign();

        // 将DTO属性复制到实体对象
        BeanUtils.copyProperties(productDesignDTO, productDesign);

        // 生成UUID作为ID
        productDesign.setId(UUID.fastUUID().toString(true));

        // 设置创建时间和更新时间
        Date now = new Date();
        productDesign.setCreateTime(now);
        productDesign.setUpdateTime(now);

        // 将DTO转换为JSON存储
        productDesign.setJsonData(JSON.toJSONString(productDesignDTO));

        // 插入数据库
        return productDesignMapper.insertProductDesign(productDesign);
    }

    /**
     * 修改产品设计信息
     *
     * @param productDesign 产品设计信息
     * @return 结果
     */
    @Override
    public int updateProductDesign(ProductDesign productDesign)
    {
        return productDesignMapper.updateProductDesign(productDesign);
    }

    /**
     * 从DTO修改产品设计信息
     *
     * @param productDesignDTO 产品设计信息DTO
     * @return 结果
     */
    @Override
    public int updateProductDesignFromDTO(ProductDesignDTO productDesignDTO)
    {
        // 验证ID是否存在
        if (productDesignDTO.getId() == null) {
            throw new ServiceException("ID不能为空");
        }

        // 查询原有数据
        ProductDesign existingProductDesign = productDesignMapper.selectProductDesignById(productDesignDTO.getId());
        if (existingProductDesign == null) {
            throw new ServiceException("要修改的产品设计信息不存在");
        }

        // 将DTO属性复制到实体对象，保留原有的创建时间等信息
        BeanUtils.copyProperties(productDesignDTO, existingProductDesign, "createBy", "createTime", "id");

        // 设置更新时间
        existingProductDesign.setUpdateTime(new Date());

        // 更新JSON数据
        existingProductDesign.setJsonData(JSON.toJSONString(productDesignDTO));

        // 更新数据库
        return productDesignMapper.updateProductDesign(existingProductDesign);
    }

    /**
     * 批量删除产品设计信息
     *
     * @param ids 需要删除的产品设计信息主键
     * @return 结果
     */
    @Override
    public int deleteProductDesignByIds(String[] ids)
    {
        return productDesignMapper.deleteProductDesignByIds(ids);
    }

    /**
     * 删除产品设计信息信息
     *
     * @param id 产品设计信息主键
     * @return 结果
     */
    @Override
    public int deleteProductDesignById(String id)
    {
        return productDesignMapper.deleteProductDesignById(id);
    }
}
