package cn.dream.blade.service.impl;

import cn.dream.blade.domain.BoxInfo;
import cn.dream.blade.domain.dto.BoxInfoAnalysis;
import cn.dream.blade.domain.dto.BoxInfoDTO;
import cn.dream.blade.domain.request.AddBoxRequest;
import cn.dream.blade.listener.BatchImportListener;
import cn.dream.blade.mapper.BoxInfoMapper;
import cn.dream.blade.service.IBoxInfoService;
import cn.dream.common.exception.ServiceException;
import cn.dream.common.utils.file.FileUploadUtils;
import cn.dream.oss.service.MinioHelper;
import cn.hutool.core.collection.CollectionUtil;
import cn.idev.excel.EasyExcel;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

/**
 * 盒子信息Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class BoxInfoServiceImpl implements IBoxInfoService {
    @Resource
    private BoxInfoMapper boxInfoMapper;


    @Resource
    private MinioHelper minioHelper;



    /**
     * 根据ID查询盒子信息
     *
     * @param id 盒子信息ID
     * @return 盒子信息
     */
    @Override
    public BoxInfoDTO selectBoxInfoById(String id) {
        BoxInfoDTO dto = new BoxInfoDTO();
        BoxInfo boxInfo = boxInfoMapper.selectBoxInfoById(id);
        BeanUtils.copyProperties(boxInfo, dto);
        return dto;
    }

    @Override
    public List<BoxInfoDTO> selectBoxInfoByIds(List<String> ids) {
        log.info("根据ID查询盒子信息: {}", ids);
        List<BoxInfo> boxInfos = boxInfoMapper.selectBoxInfoByIds(ids);
        if (CollectionUtil.isNotEmpty(boxInfos)) {
            return boxInfos.stream().map(BoxInfoDTO::convertToBoxInfoDTO).toList();
        }
        return Collections.emptyList();
    }


    /**
     * 新增盒子信息
     *
     * @param addBoxRequest 盒子信息DTO
     * @return 结果
     */
    @Override
    public int insertBoxInfo(AddBoxRequest addBoxRequest) {
        // 创建盒子信息实体
        BoxInfo boxInfo = new BoxInfo();
        // 将DTO属性复制到实体
        BeanUtils.copyProperties(addBoxRequest, boxInfo);
        MultipartFile closeBox = null;
        MultipartFile dieline = null;
        try {
            closeBox = FileUploadUtils.base64ToMultipartFile(addBoxRequest.getBoxCloseUrl(), null);
            dieline = FileUploadUtils.base64ToMultipartFile(addBoxRequest.getDieline(), null);
        } catch (IOException e) {
            log.error("图片base64 to file失败", e);
            throw new ServiceException("更新盒型:" + addBoxRequest.getId() + "数据失败");
        }
        String closeBoxUrl = null;
        String dielineUrl = null;
        try {
            closeBoxUrl = minioHelper.putObject(closeBox);
            dielineUrl = minioHelper.putObject(dieline);
        } catch (Exception e) {
            log.error("图片上传失败", e);
            throw new ServiceException("更新盒型:" + addBoxRequest.getId() + "数据失败");
        }
        boxInfo.setBoxCloseUrl(closeBoxUrl);
        boxInfo.setDieline(dielineUrl);
        log.info("新增盒子信息: {}", boxInfo);
        return boxInfoMapper.insertBoxInfo(boxInfo);
    }



    @Override
    public List<String> analysisExcel(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            log.error("解析Excel失败：文件为空");
            throw new ServiceException("上传的Excel文件为空");
        }
        try {
            // 创建监听器
            BatchImportListener listener = new BatchImportListener();
            // 清空监听器中的数据
            listener.clear();

            // 使用EasyExcel解析Excel文件
            try {
                EasyExcel.read(file.getInputStream(), BoxInfoAnalysis.class, listener).sheet().doRead();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }

            // 获取解析到的ID列表
            List<String> ids = listener.getAllIds();

            log.info("成功解析Excel文件，共获取{}\u4e2aID", ids.size());
            return ids;

        } catch (Exception e) {
            log.error("解析Excel文件失败: {}", e.getMessage(), e);
            throw new ServiceException("解析Excel文件失败: " + e.getMessage());
        }
    }

}
