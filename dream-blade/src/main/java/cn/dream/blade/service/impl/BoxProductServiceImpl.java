package cn.dream.blade.service.impl;

import cn.dream.blade.domain.BoxProduct;
import cn.dream.blade.domain.dto.ProductDTO;
import cn.dream.blade.mapper.BoxProductMapper;
import cn.dream.blade.domain.request.ProductListRequest;
import cn.dream.blade.domain.request.ProductRequest;
import cn.dream.blade.domain.request.RecommendProductRequest;
import cn.dream.blade.service.IBoxProductService;
import cn.dream.common.utils.bean.BeanUtils;
import cn.dream.common.utils.uuid.UUID;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 盒子产品Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class BoxProductServiceImpl  implements IBoxProductService   {
    @Resource
    private BoxProductMapper boxProductMapper;


    @Override
    public void batchAddRecommend(List<RecommendProductRequest> recommendProductRequests) {
        //获取当前用户信息
        BoxProduct product = new BoxProduct();
        product.setRecordId(UUID.randomUUID().toString());
        List<BoxProduct> productList = recommendProductRequests.stream().map(recommendProductRequest -> {
            product.setProductName(recommendProductRequest.getProductName());
            product.setBoxId(recommendProductRequest.getBoxId());
            product.setBoxImg(recommendProductRequest.getBoxImg());
            product.setType(1);
            product.setUserId(product.getUserId());
            product.setWidth(recommendProductRequest.getWidth());
            product.setHeight(recommendProductRequest.getHeight());
            product.setLength(recommendProductRequest.getLength());
            product.setMaterialName(recommendProductRequest.getMaterialName());
            return product;
        }).toList();
        boxProductMapper.insertBatchSomeColumn(productList);
    }

    @Override
    public void getProductList(ProductListRequest request) {
        ProductDTO dto=new ProductDTO();
        BeanUtils.copyProperties(request,dto);
        BoxProduct product = boxProductMapper.selectByParams(dto);

        return;
    }

    @Override
    public void deleteProduct(String id) {
        boxProductMapper.deleteById(id);
    }

    @Override
    public void addProduct(ProductRequest productRequest) {

    }
}



