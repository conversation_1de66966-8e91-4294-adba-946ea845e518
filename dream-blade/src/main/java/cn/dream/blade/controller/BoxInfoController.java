package cn.dream.blade.controller;

import cn.dream.blade.domain.BoxInfo;
import cn.dream.blade.dto.BoxInfoDTO;
import cn.dream.blade.service.IBoxInfoService;
import cn.dream.common.annotation.Anonymous;
import cn.dream.common.annotation.Log;
import cn.dream.common.core.controller.BaseController;
import cn.dream.common.core.domain.AjaxResultMap;
import cn.dream.common.core.page.TableDataInfo;
import cn.dream.common.enums.BusinessType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 盒子信息Controller
 *
 * <AUTHOR>
 */
@Tag(name = "盒子信息接口", description = "用于管理盒子信息，包括盒子尺寸、材质、颜色、设计区域等信息")
@RestController
@RequestMapping("/blade/boxinfo")
@Anonymous
public class BoxInfoController extends BaseController
{
    @Autowired
    private IBoxInfoService boxInfoService;

    /**
     * 查询盒子信息列表
     */
    @GetMapping("/list")
    @Operation(summary = "查询盒子信息列表", description = "支持按照盒子尺寸、材质、颜色等字段进行查询，返回分页结果")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "查询成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = TableDataInfo.class)))
    })
    public TableDataInfo list(BoxInfo boxInfo)
    {
        startPage();
        List<BoxInfo> list = boxInfoService.selectBoxInfoList(boxInfo);
        return getDataTable(list);
    }

    /**
     * 获取盒子信息详细信息
     */
    @GetMapping(value = "/{id}")
    @Operation(summary = "获取盒子信息详细信息", description = "根据ID获取盒子信息详细信息")
    @Parameter(name = "id", description = "盒子信息ID", required = true, schema = @Schema(type = "string", example = "BOX123456"), in = ParameterIn.PATH)
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "查询成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = AjaxResultMap.class)))
    })
    public AjaxResultMap getInfo(@PathVariable("id") String id)
    {
        return success(boxInfoService.selectBoxInfoById(id));
    }

    /**
     * 新增盒子信息
     */
    @Log(title = "盒子信息", businessType = BusinessType.INSERT)
    @PostMapping
    @Operation(summary = "新增盒子信息", description = "新增盒子信息，id字段需要传入，作为主键")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
        description = "盒子信息对象",
        required = true,
        content = @Content(
            mediaType = "application/json",
            schema = @Schema(implementation = BoxInfoDTO.class),
            examples = {
                @ExampleObject(name = "示例", value = "{\"id\":\"BOX123456\",\"width\":100.5,\"height\":50.2,\"length\":150.0,\"thickness\":0.5,\"materialName\":\"纸板\",\"packageInsideColor\":\"#FFFFFF\",\"packageOutsideColor\":\"#000000\"}")
            }
        )
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "新增成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = AjaxResultMap.class)))
    })
    public AjaxResultMap add(@RequestBody BoxInfoDTO boxInfoDTO)
    {
        BoxInfo boxInfo = new BoxInfo();
        boxInfo.setId(boxInfoDTO.getId());
        boxInfo.setWidth(boxInfoDTO.getWidth());
        boxInfo.setHeight(boxInfoDTO.getHeight());
        boxInfo.setLength(boxInfoDTO.getLength());
        boxInfo.setThickness(boxInfoDTO.getThickness());
        boxInfo.setMaterialName(boxInfoDTO.getMaterialName());
        boxInfo.setMaterialImage(boxInfoDTO.getMaterialImage());
        boxInfo.setScreenshot(boxInfoDTO.getScreenshot());
        boxInfo.setPackageInsideColor(boxInfoDTO.getPackageInsideColor());
        boxInfo.setPackageOutsideColor(boxInfoDTO.getPackageOutsideColor());
        boxInfo.setDesignArea(boxInfoDTO.getDesignArea());
        boxInfo.setWidthOfArea(boxInfoDTO.getWidthOfArea());
        boxInfo.setHeightOfArea(boxInfoDTO.getHeightOfArea());
        boxInfo.setBoxUrl(boxInfoDTO.getBoxUrl());
        return toAjax(boxInfoService.insertBoxInfo(boxInfo));
    }

    /**
     * 修改盒子信息
     */
    @Log(title = "盒子信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @Operation(summary = "修改盒子信息", description = "修改盒子信息，id字段必须传入，作为修改条件")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
        description = "盒子信息对象",
        required = true,
        content = @Content(
            mediaType = "application/json",
            schema = @Schema(implementation = BoxInfoDTO.class)
        )
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "修改成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = AjaxResultMap.class)))
    })
    public AjaxResultMap edit(@RequestBody BoxInfoDTO boxInfoDTO)
    {
        BoxInfo boxInfo = new BoxInfo();
        boxInfo.setId(boxInfoDTO.getId());
        boxInfo.setWidth(boxInfoDTO.getWidth());
        boxInfo.setHeight(boxInfoDTO.getHeight());
        boxInfo.setLength(boxInfoDTO.getLength());
        boxInfo.setThickness(boxInfoDTO.getThickness());
        boxInfo.setMaterialName(boxInfoDTO.getMaterialName());
        boxInfo.setMaterialImage(boxInfoDTO.getMaterialImage());
        boxInfo.setScreenshot(boxInfoDTO.getScreenshot());
        boxInfo.setPackageInsideColor(boxInfoDTO.getPackageInsideColor());
        boxInfo.setPackageOutsideColor(boxInfoDTO.getPackageOutsideColor());
        boxInfo.setDesignArea(boxInfoDTO.getDesignArea());
        boxInfo.setWidthOfArea(boxInfoDTO.getWidthOfArea());
        boxInfo.setHeightOfArea(boxInfoDTO.getHeightOfArea());
        boxInfo.setBoxUrl(boxInfoDTO.getBoxUrl());
        return toAjax(boxInfoService.updateBoxInfo(boxInfo));
    }

    /**
     * 删除盒子信息
     */
    @Log(title = "盒子信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @Operation(summary = "删除盒子信息", description = "根据ID批量删除盒子信息")
    @Parameter(name = "ids", description = "盒子信息ID数组（支持批量删除）", required = true, schema = @Schema(type = "array", example = "[\"BOX123\",\"BOX456\"]"), in = ParameterIn.PATH)
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "删除成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = AjaxResultMap.class)))
    })
    public AjaxResultMap remove(@PathVariable String[] ids)
    {
        return toAjax(boxInfoService.deleteBoxInfoByIds(ids));
    }
}
