package cn.dream.blade.controller;

import cn.dream.blade.domain.entity.ImageCommand;
import cn.dream.blade.domain.request.*;
import cn.dream.blade.service.IImageCommandService;
import cn.dream.common.annotation.Log;
import cn.dream.common.core.controller.BaseController;
import cn.dream.common.core.domain.AjaxResultMap;
import cn.dream.common.core.page.TableDataInfo;
import cn.dream.common.enums.BusinessType;
import cn.dream.common.enums.OperatorType;
import cn.dream.common.utils.SecurityUtils;
import cn.dream.common.utils.bean.BeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 图像指令Controller
 *
 * <AUTHOR>
 */
@Tag(name = "图像指令接口", description = "用于管理图像指令信息，包括描述词、比例、布局、尺寸等信息")
@RestController
@RequestMapping("/blade/imageCommand")
public class ImageCommandController extends BaseController {

    @Resource
    private IImageCommandService imageCommandService;

    /**
     * 新增图像指令
     */
    @Operation(summary = "新增图像指令")
    @PostMapping("/add")
    @Log(title = "新增图像指令", businessType = BusinessType.INSERT, operatorType = OperatorType.WEB)
    public AjaxResultMap add(@RequestBody @Valid AddImageCommandRequest request) {
        ImageCommand imageCommand = new ImageCommand();
        BeanUtils.copyProperties(request, imageCommand);
        // 设置当前用户ID
        imageCommand.setUserId(Math.toIntExact(getUserId()));
        return toAjax(imageCommandService.insertImageCommand(imageCommand));
    }

    /**
     * 修改图像指令
     */
    @Operation(summary = "修改图像指令")
    @PostMapping("/update")
    @Log(title = "修改图像指令", businessType = BusinessType.UPDATE, operatorType = OperatorType.WEB)
    public AjaxResultMap update(@RequestBody @Valid UpdateImageCommandRequest request) {
        ImageCommand imageCommand = new ImageCommand();
        BeanUtils.copyProperties(request, imageCommand);
        return toAjax(imageCommandService.updateImageCommand(imageCommand));
    }

    /**
     * 查询图像指令详情
     */
    @Operation(summary = "查询图像指令详情")
    @PostMapping("/getInfo")
    public AjaxResultMap getInfo(@RequestBody @Valid GetImageCommandRequest request) {
        ImageCommand imageCommand = imageCommandService.selectImageCommandById(request.getId());
        return success(imageCommand);
    }

    /**
     * 查询图像指令列表
     */
    @Operation(summary = "查询图像指令列表")
    @PostMapping("/list")
    public AjaxResultMap list(@RequestBody ImageCommandQueryRequest request) {
        ImageCommand imageCommand = new ImageCommand();
        BeanUtils.copyProperties(request, imageCommand);
        List<ImageCommand> list = imageCommandService.selectImageCommandList(imageCommand);
        return success(list);
    }

    /**
     * 分页查询当前用户的图像指令列表
     */
    @Operation(summary = "分页查询当前用户的图像指令列表")
    @PostMapping("/listByUserId")
    public TableDataInfo listByUserId() {
        startPage();
        Integer userId = Math.toIntExact(getUserId());
        List<ImageCommand> list = imageCommandService.selectImageCommandListByUserId(userId);
        return getDataTable(list);
    }

    /**
     * 分页查询图像指令列表（带查询条件）
     */
    @Operation(summary = "分页查询图像指令列表")
    @PostMapping("/pageList")
    public TableDataInfo pageList(@RequestBody ImageCommandQueryRequest request) {
        startPage();
        ImageCommand imageCommand = new ImageCommand();
        BeanUtils.copyProperties(request, imageCommand);
        List<ImageCommand> list = imageCommandService.selectImageCommandList(imageCommand);
        return getDataTable(list);
    }

    /**
     * 删除图像指令
     */
    @Operation(summary = "删除图像指令")
    @DeleteMapping("/delete/{id}")
    @Log(title = "删除图像指令", businessType = BusinessType.DELETE, operatorType = OperatorType.WEB)
    public AjaxResultMap delete(@Parameter(description = "图像指令主键ID", required = true) @PathVariable Integer id) {
        return toAjax(imageCommandService.deleteImageCommandById(id));
    }

    /**
     * 批量删除图像指令
     */
    @Operation(summary = "批量删除图像指令")
    @PostMapping("/deleteBatch")
    @Log(title = "批量删除图像指令", businessType = BusinessType.DELETE, operatorType = OperatorType.WEB)
    public AjaxResultMap deleteBatch(@RequestBody @Valid DeleteImageCommandRequest request) {
        return toAjax(imageCommandService.deleteImageCommandByIds(request.getIds()));
    }
}
