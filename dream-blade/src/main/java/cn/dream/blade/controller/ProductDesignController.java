package cn.dream.blade.controller;

import cn.dream.blade.domain.dto.ProductDesignDTO;
import cn.dream.blade.service.IProductDesignService;
import cn.dream.common.annotation.Log;
import cn.dream.common.core.controller.BaseController;
import cn.dream.common.core.domain.AjaxResultMap;
import cn.dream.common.enums.BusinessType;
import cn.dream.common.enums.OperatorType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 产品设计信息Controller
 *
 * <AUTHOR>
 */
@Tag(name = "产品设计信息接口", description = "用于管理产品设计信息，包括产品名称、包装类型、设计风格、设计元素、材料、颜色等信息")
@RestController
@RequestMapping("/blade/product")
@PreAuthorize("isAuthenticated()")
public class ProductDesignController extends BaseController
{
    @Resource
    private IProductDesignService productDesignService;

    /**
     * 从JSON字符串新增产品设计信息
     */
    @PostMapping("/json")
    @Operation(summary = "从JSON字符串新增产品设计信息", description = "直接传入JSON字符串，系统会自动解析并保存到数据库中")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
        description = "JSON字符串",
        required = true,
        content = @Content(
            mediaType = "application/json",
            schema = @Schema(type = "string"),
            examples = {
                @ExampleObject(name = "示例", value = "{\"productName\":\"高档钢笔\",\"packageType\":\"对盖盒\",\"designStyle\":\"现代简约\",\"designElement\":\"品牌标识与文字\",\"material\":\"白卡纸\",\"color\":\"蓝色、橙色\"}")
            }
        )
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "新增成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = AjaxResultMap.class))),
        @ApiResponse(responseCode = "500", description = "新增失败", content = @Content(mediaType = "application/json"))
    })
    @Log(title = "用户联想",businessType = BusinessType.IMPORT,operatorType = OperatorType.WEB)
    public AjaxResultMap addFromJson(@RequestBody ProductDesignDTO designDTO)
    {
        return toAjax(productDesignService.insertProductDesignFromDTO(designDTO));
    }

}
