package cn.dream.blade.controller;

import cn.dream.blade.domain.request.AddProductListRequest;
import cn.dream.blade.domain.request.EditProductRequest;
import cn.dream.blade.domain.request.GetProductListRequest;
import cn.dream.blade.domain.request.AddProductRequest;
import cn.dream.blade.service.IBoxProductService;
import cn.dream.common.annotation.Anonymous;
import cn.dream.common.annotation.Log;
import cn.dream.common.core.controller.BaseController;
import cn.dream.common.core.domain.AjaxResultMap;
import cn.dream.common.enums.BusinessType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

/**
 * 盒子产品Controller
 *
 * <AUTHOR>
 */
@Tag(name = "盒子产品接口", description = "用于管理盒子产品信息，包括推荐产品和最终产品")
@RestController
@RequestMapping("/blade/boxProduct")
@Anonymous
public class BoxProductController extends BaseController {
    @Resource
    private IBoxProductService boxProductService;


    /**
     * 生成盒子产品
     */
    @Log(title = "盒子产品", businessType = BusinessType.INSERT)
    @PostMapping("/addRecommend")
    @Operation(summary = "新增盒子推荐", description = "批量新增推荐盒子产品信息")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "盒子推荐对象",
            required = true,
            content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = AddProductListRequest.class)
            )
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "新增成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = AjaxResultMap.class))),
            @ApiResponse(responseCode = "500", description = "新增失败", content = @Content(mediaType = "application/json"))
    })
    public AjaxResultMap addRecommend(@RequestBody AddProductListRequest request) {
        return AjaxResultMap.success(boxProductService.batchAddRecommend(request.getProductList()));
    }


    @Log(title = "盒子产品", businessType = BusinessType.INSERT)
    @PostMapping("/addProduct")
    @Operation(summary = "新增盒子产品", description = "新增盒子产品信息")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "盒子产品对象",
            required = true,
            content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = AddProductRequest.class)
            )
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "新增成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = AjaxResultMap.class))),
            @ApiResponse(responseCode = "500", description = "新增失败", content = @Content(mediaType = "application/json"))
    })
    public AjaxResultMap addProduct(@RequestBody AddProductRequest addProductRequest) {
        boxProductService.addProduct(addProductRequest);
        return success();
    }

    @Log(title = "盒子产品", businessType = BusinessType.UPDATE)
    @PostMapping("/updateProduct")
    @Operation(summary = "确定盒子产品", description = "确定盒子产品信息")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "盒子产品对象",
            required = true,
            content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = EditProductRequest.class)
            )
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "新增成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = AjaxResultMap.class))),
            @ApiResponse(responseCode = "500", description = "新增失败", content = @Content(mediaType = "application/json"))
    })
    public AjaxResultMap updateProduct(@RequestBody EditProductRequest editProductRequest) {
        boxProductService.editProduct(editProductRequest);
        return success();
    }


    @Operation(summary = "盒子历史记录", description = "查询盒子历史版本记录")
    @PostMapping("/list")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "新查询成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = AjaxResultMap.class))),
            @ApiResponse(responseCode = "500", description = "查询失败", content = @Content(mediaType = "application/json"))
    })
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "查询盒子产品生成记录",
            required = true,
            content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = GetProductListRequest.class)
            )
    )
    public AjaxResultMap productList(@RequestBody GetProductListRequest request) {
        return AjaxResultMap.success(boxProductService.getProductList(request));
    }

    @Log(title = "盒子产品", businessType = BusinessType.DELETE)
    @Operation(summary = "删除记录", description = "删除历史记录")
    @DeleteMapping("/delete/{id}")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "新增成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = AjaxResultMap.class))),
            @ApiResponse(responseCode = "500", description = "新增失败", content = @Content(mediaType = "application/json"))
    })
    public AjaxResultMap deleteProduct(@Parameter(description = "产品主键id", required = true) @PathVariable String id) {
        boxProductService.deleteProduct(id);
        return success();
    }

}
