package cn.dream.blade.mapper;

import cn.dream.blade.domain.BoxInfo;
import cn.dream.blade.domain.dto.BoxInfoDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 盒子信息Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface BoxInfoMapper {



    BoxInfo selectBoxInfoById(@Param("id") String id);


    List<BoxInfo> selectBoxInfoByIds(@Param("ids") List<String> ids);



    int insertBoxInfo(BoxInfo boxInfo);


}
