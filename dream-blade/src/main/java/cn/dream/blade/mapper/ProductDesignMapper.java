package cn.dream.blade.mapper;

import cn.dream.blade.domain.entity.ProductDesign;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 产品设计信息Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductDesignMapper
{

    /**
     * 查询产品设计信息列表
     *
     * @param productDesign 产品设计信息
     * @return 产品设计信息集合
     */
     List<ProductDesign> selectProductDesignList(ProductDesign productDesign);

    /**
     * 新增产品设计信息
     *
     * @param productDesign 产品设计信息
     * @return 结果
     */
     int insertProductDesign(ProductDesign productDesign);

}
