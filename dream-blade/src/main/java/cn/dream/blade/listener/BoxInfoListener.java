package cn.dream.blade.listener;

import cn.dream.blade.domain.excel.BoxInfoAnalysis;
import cn.dream.blade.mapper.BoxInfoMapper;
import cn.dream.blade.service.IBoxInfoService;


import cn.dream.common.exception.ServiceException;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Excel批量导入监听器
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class BoxInfoListener extends AnalysisEventListener<BoxInfoAnalysis> {


    private final BoxInfoMapper boxInfoMapper; // 盒子信息服务类

    @Autowired
    public BoxInfoListener(BoxInfoMapper boxInfoMapper) {
        this.boxInfoMapper = boxInfoMapper;
    }

    private static final int BATCH_SIZE = 2; // 每批次处理量

    // 批量处理列表
    private final List<BoxInfoAnalysis> batchList = new ArrayList<>();

    /**
     * -- GETTER --
     *  获取所有读取到的ID
     *
     * @return ID列表
     */
    // 存储所有读取到的ID
    @Getter
    private final List<String> allIds = new ArrayList<>();

    /**
     * 每读取一行数据时调用
     *
     * @param data 读取到的数据
     * @param context 解析上下文
     */
    @Override
    public void invoke(BoxInfoAnalysis data, AnalysisContext context) {
        // 添加到批处理列表
        batchList.add(data);
        // 收集ID
        if (data != null && data.getModeid() != null && !data.getModeid().isEmpty()) {
            allIds.add(data.getModeid());
            log.debug("读取到ID: {}, data: {}", data.getModeid(), JSONObject.toJSONString(data));
        }
        // 当批处理列表达到阈值时进行批量处理
        if (batchList.size() >= BATCH_SIZE) {
            processBatch();
            batchList.clear();
        }
    }

    /**
     * 所有数据解析完成后调用
     *
     * @param context 解析上下文
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 处理剩余数据
        if (!batchList.isEmpty()) {
            processBatch();
            batchList.clear();
        }
        log.info("共读取到{}\u4e2aID", allIds.size());
    }

    /**
     * 批量处理数据
     */
    private void processBatch() {
        try {
            log.info("批量处理{}\u6761数据", batchList.size());
            // 调用服务层方法进行批量处理
            boxInfoMapper.updateOrInsert(BoxInfoAnalysis.convertToBoxInfo(batchList));
        } catch (Exception e) {
            log.error("批量处理数据失败: {}", e.getMessage(), e);
            throw new ServiceException("批处理数据失败");
        }
    }

    /**
     * 清空所有数据，便于重新使用
     */
    public void clear() {
        batchList.clear();
        allIds.clear();
    }
}
