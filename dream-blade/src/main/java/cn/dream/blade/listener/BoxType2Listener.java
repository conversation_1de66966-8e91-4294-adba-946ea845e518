package cn.dream.blade.listener;

import cn.dream.blade.domain.excel.BoxType2Analysis;
import cn.dream.blade.domain.excel.BoxTypeAnalysis;
import cn.dream.blade.mapper.BoxTypeInfoMapper;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Excel批量导入监听器
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class BoxType2Listener extends AnalysisEventListener<BoxType2Analysis> {


    private final BoxTypeInfoMapper boxTypeInfoMapper;

    @Autowired
    public BoxType2Listener(BoxTypeInfoMapper boxTypeInfoMapper) {
        this.boxTypeInfoMapper = boxTypeInfoMapper;
    }

    private static final int BATCH_SIZE = 500; // 每批次处理量

    // 批量处理列表
    private final List<BoxType2Analysis> batchList = new ArrayList<>();

    /**
     * -- GETTER --
     * 获取所有读取到的ID
     *
     * @return ID列表
     */
    // 存储所有读取到的ID
    @Getter
    private final List<String> allIds = new ArrayList<>();

    /**
     * 每读取一行数据时调用
     *
     * @param data    读取到的数据
     * @param context 解析上下文
     */
    @Override
    public void invoke(BoxType2Analysis data, AnalysisContext context) {
        // 添加到批处理列表
        batchList.add(data);
        // 收集ID
        if (data != null && data.getId() != null && !data.getId().isEmpty()) {
            allIds.add(data.getId());
            log.debug("读取到ID: {}, 数据: {}", data.getId(), JSONObject.toJSONString(data));
        }
        // 当批处理列表达到阈值时进行批量处理
        if (batchList.size() >= BATCH_SIZE) {
            processBatch();
            batchList.clear();
        }
    }

    /**
     * 所有数据解析完成后调用
     *
     * @param context 解析上下文
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 处理剩余数据
        if (!batchList.isEmpty()) {
            processBatch();
            batchList.clear();
        }
        log.info("共读取到{}\u4e2aID", allIds.size());
    }

    /**
     * 批量处理数据
     */
    private void processBatch() {
        try {
            log.info("批量处理{}\u6761数据", batchList.size());
            // 调用服务层方法进行批量处理
            boxTypeInfoMapper.updateOrInsert(BoxType2Analysis.convertToBoxTypeInfo(batchList));
        } catch (Exception e) {
            log.error("批量处理数据失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 清空所有数据，便于重新使用
     */
    public void clear() {
        batchList.clear();
        allIds.clear();
    }
}
