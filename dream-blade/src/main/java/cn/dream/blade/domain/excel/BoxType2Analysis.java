package cn.dream.blade.domain.excel;

import cn.dream.blade.domain.entity.BoxTypeInfo;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.List;

@Data
public class BoxType2Analysis {

    @ExcelProperty("id")
    private Integer id;

    @ExcelProperty("name")
    private String name;

    @ExcelProperty("primaryId")
    private Integer primaryId;

    public static List<BoxTypeInfo> convertToBoxTypeInfo(List<BoxType2Analysis> boxTypeAnalyses) {
        return boxTypeAnalyses.stream().map(boxTypeAnalysis -> {
            BoxTypeInfo boxTypeInfo = new BoxTypeInfo();
            boxTypeInfo.setId(boxTypeAnalysis.getId());
            boxTypeInfo.setName(boxTypeAnalysis.getName());
            boxTypeInfo.setPrimaryId(boxTypeAnalysis.getPrimaryId());
            return boxTypeInfo;
        }).toList();
    }
}
