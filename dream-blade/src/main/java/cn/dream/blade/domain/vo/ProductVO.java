package cn.dream.blade.domain.vo;

import cn.dream.blade.domain.BoxProduct;
import cn.dream.common.utils.bean.BeanUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ProductVO {


    private Integer id;

    private String productName;

    private String boxId;

    private String boxImg;

    private BigDecimal width;

    private BigDecimal height;

    private BigDecimal length;

    private String materialName;

    private String recordId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    public static ProductVO convertToProductVO(BoxProduct boxProduct) {
        // 根据 BoxProduct 转换为 ProductVO 的逻辑
        ProductVO productVO = new ProductVO();
        BeanUtils.copyProperties(boxProduct, productVO);
        return productVO;
    }
}
