package cn.dream.blade.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 盒子产品对象 box_product
 *
 * <AUTHOR>
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BoxProduct implements Serializable {

    private static final long serialVersionUID = 1L;



    private Integer id;



    private String recordId;



    private String productName;



    private String boxId;



    private String boxImg;


    private Integer type;



    private String userId;



    private BigDecimal width;



    private BigDecimal height;



    private BigDecimal length;


    private BigDecimal thickness;


    private String materialName;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    private Integer isDelete;


    public BoxProduct(Integer id, String recordId, String productName, String boxId, String boxImg, Integer type, String userId, BigDecimal width, BigDecimal height, BigDecimal length, String materialName, Date createTime) {
        this.id = id;
        this.recordId = recordId;
        this.productName = productName;
        this.boxId = boxId;
        this.boxImg = boxImg;
        this.type = type;
        this.userId = userId;
        this.width = width;
        this.height = height;
        this.length = length;
        this.materialName = materialName;
        this.createTime = createTime;
    }
}
