package cn.dream.blade.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * 产品查询DTO
 *
 * <AUTHOR>
 */
@Data
public class ProductDTO {

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 记录ID列表
     */
    private List<String> recordIds;

    /**
     * 盒子ID
     */
    private String boxId;

    /**
     * 盒子材质
     */
    private String materialName;
}
