package cn.dream.blade.domain.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class AddProductRequest {


    @Schema(description = "记录id",requiredMode = Schema.RequiredMode.REQUIRED)
    private String recordId;

    @Schema(description = "用户id",requiredMode = Schema.RequiredMode.REQUIRED)
    private String userId;

    @Schema(description = "产品名称",requiredMode = Schema.RequiredMode.REQUIRED)
    private String productName;

    @Schema(description = "盒子id",requiredMode = Schema.RequiredMode.REQUIRED)
    private String boxId;

    @Schema(description = "盒子生成图",requiredMode = Schema.RequiredMode.REQUIRED)
    private String boxImg;

    @Schema(description = "宽度",requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal width;

    @Schema(description = "高度",requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal height;

    @Schema(description = "长度",requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal length;

    @Schema(description = "材质",requiredMode = Schema.RequiredMode.REQUIRED)
    private String materialName;

}
