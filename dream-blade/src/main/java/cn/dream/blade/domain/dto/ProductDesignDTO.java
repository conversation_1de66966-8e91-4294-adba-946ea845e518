package cn.dream.blade.domain.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 产品设计信息DTO对象，用于接收前端传入的数据
 *
 * <AUTHOR>
 */
@Data
public class ProductDesignDTO {


    /**
     * 产品名称，例如："钢笔"
     */
    private String productName;

    /**
     * 包装类型，例如："对盖盒"
     */
    private String packageType;

    /**
     * 设计风格，例如："现代简约"
     */
    private String designStyle;

    /**
     * 设计元素，例如："品牌标识与文字"
     */
    private String designElement;

    /**
     * 材料，例如："白卡纸"
     */
    private String material;

    /**
     * 颜色，例如："蓝色、橙色"
     */
    private String color;

    /**
     * 备注
     */
    private String remark;


    /**
     * 算法解析生成的JSON数据
     */
    @NotBlank(message = "JSON数据不能为空")
    private String jsonData;


    /**
     * 原始输入图片
     */
    private String inputImageUrl;

    /**
     * 原始输入的文本信息
     */
    private String inputText;
}
