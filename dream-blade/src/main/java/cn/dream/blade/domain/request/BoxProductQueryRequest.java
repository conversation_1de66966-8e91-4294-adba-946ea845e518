package cn.dream.blade.domain.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 盒子产品查询DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "盒子产品查询参数")
public class BoxProductQueryRequest {

    /**
     * 产品名称
     */
    @Schema(description = "产品名称", example = "高档礼盒")
    private String productName;

    /**
     * 盒子ID
     */
    @Schema(description = "盒子ID", example = "BOX123456")
    private String boxId;

    /**
     * 盒子材质
     */
    @Schema(description = "盒子材质", example = "白卡纸")
    private String materialName;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", example = "USER123")
    private String userId;


    private Integer pageNum;


    private Integer pageSize;

}
