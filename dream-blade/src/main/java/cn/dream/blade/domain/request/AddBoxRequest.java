package cn.dream.blade.domain.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 盒子信息数据传输对象
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "盒子信息数据传输对象")
public class AddBoxRequest {


    @Schema(description = "盒型id")
    private String id;

    @Schema(description = "盒子宽度(单位:mm)", example = "100.50")
    private BigDecimal width;


    @Schema(description = "盒子高度(单位:mm)", example = "50.20")
    private BigDecimal height;


    @Schema(description = "盒子长度(单位:mm)", example = "150.00")
    private BigDecimal length;


    @Schema(description = "盒子厚度(单位:mm)", example = "0.50")
    private BigDecimal thickness;


    @Schema(description = "盒子的材质名称", example = "纸板")
    private String materialName;


    @Schema(description = "盒子的材质图片URL")
    private String materialImage;


    @Schema(description = "用户设计的3D预览图像URL")
    private String screenshot;


    @Schema(description = "用户为盒子内部设置的颜色(十六进制)", example = "#FFFFFF")
    private String packageInsideColor;


    @Schema(description = "用户为盒子外部设置的颜色(十六进制)", example = "#000000")
    private String packageOutsideColor;


    @Schema(description = "刀线图设计范围", example = "100x200")
    private String designArea;


    @Schema(description = "设计区域的宽度(单位:mm)", example = "100.00")
    private BigDecimal widthOfArea;


    @Schema(description = "设计区域的高度(单位:mm)", example = "200.00")
    private BigDecimal heightOfArea;

    @Schema(description = "开盒base64图片")
    private String boxOpenUrl;

    @Schema(description = "闭盒base64图片")
    private String boxCloseUrl;

    @Schema(description = "刀线base64图片")
    private String dieline;


    @Schema(description = "刀线ai")
    private String aiFile;
}
