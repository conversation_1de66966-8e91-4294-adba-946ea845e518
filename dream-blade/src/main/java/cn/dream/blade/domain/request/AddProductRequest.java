package cn.dream.blade.domain.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class AddProductRequest {


    @NotBlank(message = "记录id不能为空")
    @Schema(description = "记录id",requiredMode = Schema.RequiredMode.REQUIRED)
    private String recordId;

    @NotBlank(message = "产品名称不能为空")
    @Schema(description = "产品名称",requiredMode = Schema.RequiredMode.REQUIRED)
    private String productName;

    @NotBlank(message = "盒子id不能为空")
    @Schema(description = "盒子id",requiredMode = Schema.RequiredMode.REQUIRED)
    private String boxId;

    @NotBlank(message = "盒子生成图不能为空")
    @Schema(description = "盒子生成图")
    private String boxImg;

    @Schema(description = "宽度")
    private BigDecimal width;

    @Schema(description = "高度")
    private BigDecimal height;

    @Schema(description = "长度")
    private BigDecimal length;

    @Schema(description = "厚度")
    private BigDecimal thickness;

    @Schema(description = "材质")
    private String materialName;

}
