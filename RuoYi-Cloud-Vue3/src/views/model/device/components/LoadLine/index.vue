<template>
  <div id="loadLine" class="w-full h-[374px]"></div>
</template>

<script setup lang="ts">
import { DeviceInfo } from '@/views/model/types';
import { VChart, IVChart, ILineChartSpec } from '@visactor/vchart';
import dayjs from 'dayjs';

let chart: IVChart = null;
const props = defineProps<{ data: DeviceInfo['system_load']['history'] }>();
const transformData = (data: DeviceInfo['system_load']['history']) => {
  const { cpu, gpu, ram, vram } = data;
  const cpuTransform =
    cpu?.map((item) => {
      return {
        type: 'CPU',
        time: dayjs(item.timestamp * 1000).format('HH:mm:ss'),
        value: item.value,
      };
    }) ?? [];
  const gpuTransform =
    gpu?.map((item) => {
      return {
        type: 'GPU',
        time: dayjs(item.timestamp * 1000).format('HH:mm:ss'),
        value: item.value,
      };
    }) ?? [];
  const ramTransform =
    ram?.map((item) => {
      return {
        type: '内存',
        time: dayjs(item.timestamp * 1000).format('HH:mm:ss'),
        value: item.value,
      };
    }) ?? [];
  const vramTransform =
    vram?.map((item) => {
      return {
        type: '显存',
        time: dayjs(item.timestamp * 1000).format('HH:mm:ss'),
        value: item.value,
      };
    }) ?? [];
  return [...cpuTransform, ...gpuTransform, ...ramTransform, ...vramTransform];
};
const parseSpec = (item: any): ILineChartSpec => {
  const option: ILineChartSpec = {
    type: 'line',
    data: {
      values: item,
    },
    xField: 'time',
    yField: 'value',
    seriesField: 'type',
    line: {
      style: {
        curveType: 'monotone',
      },
    },
    point: {
      style: {
        size: 0,
      },
    },
    crosshair: {
      // yField: {
      //   visible: false,
      //   line: { visible: true, type: 'line', width: 1 },
      //   label: {
      //     visible: true, // label 默认关闭
      //   },
      // },
      xField: {
        visible: true,
        line: { visible: true, type: 'line', width: 1 },
        label: {
          visible: true, // label 默认关闭
        },
      },
    },
    legends: [
      {
        visible: true,
        position: 'middle',
        orient: 'top',
      },
    ],
    axes: [
      {
        orient: 'left',
        label: {
          formatMethod(val) {
            return val + '%';
          },
        },
      },
    ],
    tooltip: {
      dimension: {
        content: {
          valueFormatter: '{value:.2z}%',
        },
      },
    },
  };
  return option;
};
const createOrUpdateChart = (data: DeviceInfo['system_load']['history']) => {
  if (!data) return;
  const container = document.getElementById('loadLine');
  const option = parseSpec(transformData(data));
  if (container && !chart) {
    chart = new VChart(option, {
      dom: container,
    });
    chart.renderSync();
  } else {
    chart.updateSpec(option);
  }
};
onMounted(() => createOrUpdateChart(props.data));
onUpdated(() => createOrUpdateChart(props.data));
onBeforeUnmount(() => chart && chart.release());
</script>
