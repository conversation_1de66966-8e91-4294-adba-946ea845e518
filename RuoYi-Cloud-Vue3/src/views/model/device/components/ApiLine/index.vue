<template>
  <div id="apiLine" class="w-1/2 h-[374px]"></div>
</template>

<script setup lang="ts">
import { VChart, IVChart, ILineChartSpec } from '@visactor/vchart';
import dayjs from 'dayjs';

let chart: IVChart = null;
const props = defineProps<{ data: any }>();
const parseSpec = (data: any[]): ILineChartSpec => {
  const option: ILineChartSpec = {
    type: 'line',
    data: {
      values: data.map((item: any) => {
        return {
          time: dayjs(item.timestamp * 1000).format('MM-DD'),
          value: item.value,
        };
      }),
    },
    title: {
      visible: true,
      text: 'API请求',
      align: 'center',
      textStyle: {
        fontSize: 14,
      },
    },
    xField: 'time',
    yField: 'value',
    line: {
      style: {
        curveType: 'monotone',
      },
    },
    point: {
      style: {
        size: 6,
      },
    },
    crosshair: {
      // yField: {
      //   visible: false,
      //   line: { visible: true, type: 'line', width: 1 },
      //   label: {
      //     visible: true, // label 默认关闭
      //   },
      // },
      xField: {
        visible: true,
        line: { visible: true, type: 'line', width: 1 },
        label: {
          visible: true, // label 默认关闭
        },
      },
    },
    tooltip: {
      dimension: {
        content: {
          key: 'Api request：',
          valueFormatter: '{value}',
        },
      },
    },
  };
  return option;
};
const createOrUpdateChart = (data: any) => {
  if (!data) return;
  const container = document.getElementById('apiLine');
  const option = parseSpec(data);
  if (container && !chart) {
    chart = new VChart(option, {
      dom: container,
    });
    chart.renderSync();
  } else {
    chart.updateSpec(option);
  }
};
onMounted(() => createOrUpdateChart(props.data));
onUpdated(() => createOrUpdateChart(props.data));
onBeforeUnmount(() => chart && chart.release());
</script>
