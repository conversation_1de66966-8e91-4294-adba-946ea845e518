export interface ModelItem {
  modelScopeId: string;
  name: string;
  avatar: string;
  downloads: number;
  libraries: string;
  path: string;
  tasks: string[];
}

export interface ModelListItem {
  customModelName: string;
  modelName: string;
  modelRegistryId: string;
  modelSource: 'modelscope' | 'custom';
  isPublic: boolean;
  storagePath: string;
  version: string;
  updateAt: string;
  status:
    | 'init'
    | 'downloading'
    | 'downloaded'
    | 'download_failed'
    | 'uploading'
    | 'uploaded'
    | 'upload_failed';
}

export interface LocalModelAdd {
  customModelName: string;
  version: string;
  description: string;
  file: File;
  isGguf: boolean;
}

export interface DeployForm {
  name?: string;
  source?: string;
  local_path?: string;
  backend?: 'llama-box';
  replicas?: number;
  description?: string;
  categories?: string[];
  backend_version?: string;
  backend_parameters?: string[];
  placement_strategy?: string;
  worker_selector?: Record<string, string>;
}

export interface DeviceInfo {
  resource_counts: {
    worker_count: number;
    gpu_count: number;
    model_count: number;
    model_instance_count: number;
  };
  system_load: {
    current: {
      cpu: number;
      gpu: number;
      ram: number;
      vram: number;
    };
    history: {
      cpu: Array<{ timestamp: number; value: number }>;
      gpu: Array<{ timestamp: number; value: number }>;
      ram: Array<{ timestamp: number; value: number }>;
      vram: Array<{ timestamp: number; value: number }>;
    };
  };
  model_usage: {
    api_request_history: any[];
    completion_token_history: any[];
    prompt_token_history: any[];
    top_users: any[];
  };
  active_models: Array<{
    id: string;
    name: string;
    token_count: number;
    instance_count: number;
    categories: string[];
    resource_claim: { ram: number; vram: number };
  }>;
}

export interface TrainLogItem {
  current_steps: number;
  elapsed_time: string;
  epoch: number;
  loss: number;
  lr: number;
  percentage: number;
  remaining_time: string;
  throughput: number;
  total_steps: number;
  total_tokens: number;
}

export interface TaskLog {
  log_content: string;
  status: 'success' | 'failed';
  trainer_log_list: TrainLogItem[];
}

export interface CredentialFormSchemas {
  defaultValue: string;
  label: string;
  max_length: number;
  options: string;
  placeholder: string;
  required: 'true' | 'false';
  type: string;
  variable: string;
  show_on: any[];
}

export interface ThirdPartyModelItem {
  provider: string;
  // preferred_provider_type: 'custom';
  configurateMethods: ('customizable-model' | 'predefined-model')[];
  currentMethod: 'customizable-model' | 'predefined-model';
  description: string;
  customConfiguration: 'active' | 'no-configure';
  helpUrl: string;
  helpWords: string;
  label: string;
  modelCredentialSchema: {
    model?: {
      label: string;
      placeholder: string;
    };
    credentialFormSchemas: CredentialFormSchemas[];
  };
  providerCredentialSchema: {
    credentialFormSchemas: CredentialFormSchemas[];
  };
  supportedModelTypes: string[];
  systemConfiguration: {
    current_quota_type: string;
    enabled: boolean;
    quota_configurations: any[];
  };
}
