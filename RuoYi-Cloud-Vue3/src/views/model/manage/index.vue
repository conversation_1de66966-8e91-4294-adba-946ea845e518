<template>
  <div class="h-full p-5 flex flex-col">
    <div class="flex items-center gap-5 mb-5">
      <span class="text-[#3e4c59] text-xl font-semibold">{{ route.meta.title }}</span>
      <!-- <span class="text-[#707e8c] text-sm">共{{ total }}个</span> -->
    </div>
    <el-space alignment="center" class="w-full justify-between mb-4">
      <el-dropdown @command="handleAdd">
        <el-button type="primary" :icon="Plus" size="large"> 导入模型 </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="online">导入在线模型</el-dropdown-item>
            <el-dropdown-item command="local">上传本地模型</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <el-space :size="16">
        <el-input
          v-model="searchForm.customModelName"
          size="large"
          class="!w-60"
          :prefix-icon="Search"
          placeholder="请输入模型名称"
          clearable
          @change="getModelList"
        />
      </el-space>
    </el-space>
    <el-card class="flex-1 !border-0" body-class="card-wrap" shadow="never">
      <div class="h-full flex-1 flex flex-col">
        <el-table :data="tableData" v-loading="loading" height="100%" row-key="modelRegistryId">
          <template #empty>
            <div class="flex flex-col items-center gap-3">
              <img :src="empty" class="w-[230px]" />
              <span class="text-xl font-semibold text-gray-800">您还没有导入任何模型</span>
            </div>
          </template>
          <el-table-column prop="customModelName" label="模型名称" width="400" fixed="left" />
          <el-table-column prop="modelSource" label="模型来源" />
          <el-table-column prop="version" label="版本号" />
          <el-table-column prop="type" label="框架类型" />
          <el-table-column prop="status" label="状态">
            <template #default="{ row }">
              <el-tag :type="modelTag[row.status]?.type">
                {{ modelTag[row.status]?.text }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="isPublic" label="是否公开">
            <template #default="{ row }">
              <el-switch
                v-model="row.isPublic"
                inline-prompt
                active-text="是"
                inactive-text="否"
                :disabled="row.modelSource !== 'custom'"
                @change="(val: boolean) => handlePublic(val, row.modelRegistryId)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="updateAt" label="更新时间" />
          <el-table-column label="操作" align="right" fixed="right">
            <template #default="{ row }">
              <!-- <el-button link type="primary" size="small" @click="handleDetail(row)">
                详情
              </el-button> -->
              <!-- <el-button v-if="row.status === 'download_failed'" link type="primary" size="small">
                重新下载
              </el-button> -->
              <el-button
                v-if="row.status === 'uploaded'"
                link
                type="primary"
                size="small"
                @click="handleAdjust(row)"
                >微调</el-button
              >
              <el-popconfirm
                title="请确认是否删除？"
                placement="top-end"
                width="200"
                @confirm="handleDelete(row.modelRegistryId)"
              >
                <template #reference>
                  <el-button link type="danger" size="small"> 删除 </el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          v-if="pagination.total > 0"
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          class="mt-2 justify-end"
          :layout="tableLayout"
          background
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    <import-models
      v-model="onlineVisible"
      :loading="confirmLoading"
      title="导入在线模型"
      @ok="handleImportOnline"
    />
    <upload-local-model v-model="localVisible" @ok="getModelList" />
  </div>
</template>

<script setup lang="ts">
import { Plus, Search } from '@element-plus/icons-vue';
import { ModelItem, ModelListItem } from '../types';
import empty from '@/assets/images/empty.png';
import { ceil } from 'lodash';
import { modelTag } from '@/constant/tag';
import ImportModels from '../components/ImportModels/index.vue';
import UploadLocalModel from '../components/UploadLocalModel/index.vue';
import { deleteModel, editLocalModel, importModel, listModels } from '@/api/model/manage';
import { ElMessage } from 'element-plus';

const route = useRoute();
const router = useRouter();
const searchForm = reactive({
  status: '',
  customModelName: '',
});
const localVisible = ref<boolean>(false);
const onlineVisible = ref<boolean>(false);
const tableData = ref<ModelListItem[]>([]);
const loading = ref<boolean>(false);
const confirmLoading = ref<boolean>(false);
const pagination = reactive({
  total: 0,
  pageNum: 1,
  pageSize: 10,
});
const tableLayout = computed(() => {
  const num = ceil(pagination.total, pagination.pageSize);
  return num > 1 ? 'total, sizes, prev, pager, next, jumper' : 'total, sizes, prev, pager, next';
});
const getModelList = async () => {
  try {
    loading.value = true;
    const { data } = await listModels({
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
      ...searchForm,
    });
    tableData.value = data.list;
    pagination.total = data.total;
  } finally {
    loading.value = false;
  }
};
const handleAdd = (command: string) => {
  if (command === 'local') {
    localVisible.value = true;
  } else {
    onlineVisible.value = true;
  }
};
const handleAdjust = (item: ModelListItem) => {
  localStorage.setItem('model', JSON.stringify(item));
  router.push({
    path: '/model/adjust/add',
    query: { modelId: item.modelRegistryId },
  });
};
const handleDelete = async (id: string) => {
  await deleteModel(id);
  ElMessage.success('删除成功');
  getModelList();
};
const handleImportOnline = async ({
  model,
  qSize,
  isGguf,
}: {
  model: ModelItem;
  qSize: string;
  isGguf: boolean;
}) => {
  try {
    confirmLoading.value = true;
    await importModel({
      name: model.name,
      path: model.path,
      Q_size: qSize,
      isGguf,
    });
    onlineVisible.value = false;
    ElMessage.success('操作成功');
    getModelList();
  } finally {
    confirmLoading.value = false;
  }
};
const handlePublic = async (val: boolean, id: string) => {
  await editLocalModel({ isPublic: val, modelRegistryId: id });
  ElMessage.success('操作成功');
};
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  getModelList();
};
const handleCurrentChange = (val: number) => {
  pagination.pageNum = val;
  getModelList();
};

onMounted(() => {
  getModelList();
});
</script>

<style lang="scss" scoped>
:deep(.card-wrap) {
  height: 100%;
  display: flex;
  justify-content: center;
}
</style>
