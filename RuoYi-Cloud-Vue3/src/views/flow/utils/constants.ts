import { k } from 'node_modules/vite/dist/node/types.d-aGj9QkWt';
import { AnswerDefault, EndNodeDefault, LLMDefault, StartNodeDefault } from './default';
import { BlockEnum, NodeTitleEnum } from './types';

type NodesExtraData = {
  about: string;
  availablePrevNodes: BlockEnum[];
  availableNextNodes: BlockEnum[];
  getAvailablePrevNodes: (isChatMode: boolean) => BlockEnum[];
  getAvailableNextNodes: (isChatMode: boolean) => BlockEnum[];
  checkValid: any;
};

export const NODE_MENU_SORT = [
  {
    title: '',
    menus: [
      {
        label: 'LLM',
        key: 'llm',
        desc: '调用大语言模型回答问题或者对自然语言进行处理。',
      },
      {
        label: '知识检索',
        key: 'knowledge-retrieval',
        desc: '允许你从知识库中查询与用户问题相关的文本内容。',
      },
      {
        label: '结束',
        key: 'end',
        desc: '定义一个 workflow 流程的结束和结果类型。',
      },
    ],
  },
  {
    title: '问题理解',
    menus: [
      {
        label: '问题分类器',
        key: 'question-classifier',
        desc: '定义用户问题的分类条件，LLM能够根据分类描述定义对话的进展方式。',
      },
    ],
  },
  {
    title: '逻辑',
    menus: [
      {
        label: '条件分支',
        key: 'if-else',
        desc: '允许你根据 if/else 条件将workflow 拆分成两个分支。',
      },
      {
        label: '迭代',
        key: 'iteration',
        desc: '对列表对象执行多次步骤直至输出所有结果。',
      },
    ],
  },
  {
    title: '转换',
    menus: [
      {
        label: '代码执行',
        key: 'code',
        desc: '执行一段 Python 或 NodeJs 代码实现自定义逻辑。',
      },
      {
        label: '模板转换',
        key: 'template-transform',
        desc: '对用户输入进行格式化，例如将用户输入的日期格式化为日期对象。',
      },
      {
        label: '变量聚合器',
        key: 'variable-aggregator',
        desc: '将多路分支的变量聚合为一个变量，以实现下游节点统一配置。',
      },
      {
        label: '文档提取器',
        key: 'document-extractor',
        desc: '用于将用户上传的文档解析为LLM 便于理解的文本内容。',
      },
      {
        label: '参数提取器',
        key: 'parameter-extractor',
        desc: '利用 LLM 从自然语言内推理提取出结构化参数，用于后置的工具调用或 HTTP 请求。',
      },
      {
        label: '变量赋值',
        key: 'variable-assigner',
        desc: '定义变量，并赋予初始值。',
      },
    ],
  },
  {
    title: '工具',
    menus: [
      {
        label: 'HTTP 请求',
        key: 'http-request',
        desc: '允许通过 HTTP 协议发送服务器请求。',
      },
      {
        label: '列表操作',
        key: 'list-operator',
        desc: '用于过滤和排序数组内容。',
      },
    ],
  },
];

export const NODES_EXTRA_DATA: Record<string /* type BlockEnum */, NodesExtraData> = {
  [BlockEnum.Start]: {
    about: '',
    availablePrevNodes: [],
    availableNextNodes: [],
    getAvailablePrevNodes: StartNodeDefault.getAvailablePrevNodes,
    getAvailableNextNodes: StartNodeDefault.getAvailableNextNodes,
    checkValid: StartNodeDefault.checkValid,
  },
  [BlockEnum.End]: {
    about: '',
    availablePrevNodes: [],
    availableNextNodes: [],
    getAvailablePrevNodes: EndNodeDefault.getAvailablePrevNodes,
    getAvailableNextNodes: EndNodeDefault.getAvailableNextNodes,
    checkValid: EndNodeDefault.checkValid,
  },
  [BlockEnum.Answer]: {
    about: '',
    availablePrevNodes: [],
    availableNextNodes: [],
    getAvailablePrevNodes: AnswerDefault.getAvailablePrevNodes,
    getAvailableNextNodes: AnswerDefault.getAvailableNextNodes,
    checkValid: AnswerDefault.checkValid,
  },
  [BlockEnum.LLM]: {
    about: '',
    availablePrevNodes: [],
    availableNextNodes: [],
    getAvailablePrevNodes: LLMDefault.getAvailablePrevNodes,
    getAvailableNextNodes: LLMDefault.getAvailableNextNodes,
    checkValid: LLMDefault.checkValid,
  },
};

export const CUSTOM_NODE = 'custom';
export const CUSTOM_ITERATION_START_NODE = 'custom-iteration-start';
export const ITERATION_NODE_Z_INDEX = 1;
export const ITERATION_CHILDREN_Z_INDEX = 1002;
export const START_INITIAL_POSITION = { x: 80, y: 282 };
export const NODE_WIDTH = 240;
export const X_OFFSET = 60;
export const NODE_WIDTH_X_OFFSET = NODE_WIDTH + X_OFFSET;

export const ALL_CHAT_AVAILABLE_BLOCKS = Object.keys(NODES_EXTRA_DATA).filter(
  (key) => key !== BlockEnum.End && key !== BlockEnum.Start,
) as BlockEnum[];
export const ALL_COMPLETION_AVAILABLE_BLOCKS = Object.keys(NODES_EXTRA_DATA).filter(
  (key) => key !== BlockEnum.Answer && key !== BlockEnum.Start,
) as BlockEnum[];

// 初始化节点数据
export const NODES_INITIAL_DATA: Record<string /* type BlockEnum */, any> = {
  [BlockEnum.Start]: {
    title: NodeTitleEnum.Start,
    desc: '',
    ...StartNodeDefault.defaultValue,
  },
  [BlockEnum.End]: {
    title: NodeTitleEnum.End,
    desc: '',
    ...EndNodeDefault.defaultValue,
  },
  [BlockEnum.Answer]: {
    title: NodeTitleEnum.Answer,
    desc: '',
    ...AnswerDefault.defaultValue,
  },
  [BlockEnum.LLM]: {
    title: NodeTitleEnum.LLM,
    desc: '',
    variables: [],
    ...LLMDefault.defaultValue,
  },
};

export const NodeLabel: Record<string, string> = {
  [BlockEnum.Start]: NodeTitleEnum.Start,
  [BlockEnum.End]: NodeTitleEnum.End,
  [BlockEnum.Answer]: NodeTitleEnum.Answer,
  [BlockEnum.LLM]: NodeTitleEnum.LLM,
  [BlockEnum.KnowledgeRetrieval]: NodeTitleEnum.KnowledgeRetrieval,
  [BlockEnum.QuestionClassifier]: NodeTitleEnum.QuestionClassifier,
  [BlockEnum.IfElse]: NodeTitleEnum.IfElse,
  [BlockEnum.Code]: NodeTitleEnum.Code,
  [BlockEnum.TemplateTransform]: NodeTitleEnum.TemplateTransform,
  [BlockEnum.HttpRequest]: NodeTitleEnum.HttpRequest,
  [BlockEnum.VariableAssigner]: NodeTitleEnum.VariableAssigner,
  [BlockEnum.VariableAggregator]: NodeTitleEnum.VariableAggregator,
  [BlockEnum.ParameterExtractor]: NodeTitleEnum.ParameterExtractor,
  [BlockEnum.Iteration]: NodeTitleEnum.Iteration,
  [BlockEnum.DocExtractor]: NodeTitleEnum.DocExtractor,
  [BlockEnum.ListFilter]: NodeTitleEnum.ListFilter,
  [BlockEnum.IterationStart]: NodeTitleEnum.IterationStart,
  [BlockEnum.Assigner]: NodeTitleEnum.Assigner,
};
