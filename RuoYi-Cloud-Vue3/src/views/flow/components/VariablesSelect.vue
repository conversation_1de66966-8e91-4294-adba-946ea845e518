<template>
  <el-select v-model="modelValue" multiple :multiple-limit="1"></el-select>
</template>

<script setup lang="ts">
const modelValue = defineModel<string[]>({ required: true });
const options = ref([]);
const sysVars = [
  {
    label: '开始',
    key: 'sys',
    vars: [
      {
        label: 'sys.user_id',
        type: 'String',
        value: ['sys', 'user_id'],
      },
      {
        label: 'sys.files',
        type: 'Array[File]',
        value: ['sys', 'files'],
      },
      {
        label: 'sys.app_id',
        type: 'String',
        value: ['sys', 'app_id'],
      },
      {
        label: 'sys.workflow_id',
        type: 'String',
        value: ['sys', 'workflow_id'],
      },
      {
        label: 'sys.workflow_run_id',
        type: 'String',
        value: ['sys', 'workflow_run_id'],
      },
    ],
  },
];
</script>
