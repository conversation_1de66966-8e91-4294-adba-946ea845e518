import { useAppStore, useWorkflowStore } from '@/store';
import { generateNewNode } from '../utils';
import {
  NODE_WIDTH_X_OFFSET,
  NODES_INITIAL_DATA,
  START_INITIAL_POSITION,
} from '../utils/constants';
import { BlockEnum } from '../utils/types';
import { useVueFlow } from '@vue-flow/core';

const useWorkflowTemplate = () => {
  const { isChatMode } = useAppStore();
  const { setNodes } = useVueFlow();
  const { updateVariables } = useWorkflowStore();
  const { newNode: startNode } = generateNewNode({
    data: NODES_INITIAL_DATA.start,
    type: BlockEnum.Start,
    position: START_INITIAL_POSITION,
    selectable: true,
    deletable: false,
  });
  updateVariables({
    id: startNode.id,
    vars: [
      {
        label: '开始',
        key: 'start',
        vars: [
          {
            label: 'sys.user_id',
            type: 'String',
            value: ['sys', 'user_id'],
          },
          {
            label: 'sys.files',
            type: 'Array[File]',
            value: ['sys', 'files'],
          },
          {
            label: 'sys.app_id',
            type: 'String',
            value: ['sys', 'app_id'],
          },
          {
            label: 'sys.workflow_id',
            type: 'String',
            value: ['sys', 'workflow_id'],
          },
          {
            label: 'sys.workflow_run_id',
            type: 'String',
            value: ['sys', 'workflow_run_id'],
          },
        ],
      },
    ],
  });
  // if (isChatMode) {
  //   const { newNode: llmNode } = generateNewNode({
  //     id: 'llm',
  //     data: {
  //       ...NODES_INITIAL_DATA.llm,
  //       memory: {
  //         window: { enabled: false, size: 10 },
  //         query_prompt_template: '{{#sys.query#}}',
  //       },
  //       selected: true,
  //     },
  //     type: BlockEnum.LLM,
  //     position: {
  //       x: START_INITIAL_POSITION.x + NODE_WIDTH_X_OFFSET,
  //       y: START_INITIAL_POSITION.y,
  //     },
  //   } as any);

  //   const { newNode: answerNode } = generateNewNode({
  //     id: 'answer',
  //     data: {
  //       ...NODES_INITIAL_DATA.answer,
  //       answer: `{{#${llmNode.id}.text#}}`,
  //     },
  //     type: BlockEnum.Answer,
  //     position: {
  //       x: START_INITIAL_POSITION.x + NODE_WIDTH_X_OFFSET * 2,
  //       y: START_INITIAL_POSITION.y,
  //     },
  //   } as any);

  //   const startToLlmEdge = {
  //     id: `${startNode.id}-${llmNode.id}`,
  //     source: startNode.id,
  //     sourceHandle: 'source',
  //     target: llmNode.id,
  //     targetHandle: 'target',
  //   };

  //   const llmToAnswerEdge = {
  //     id: `${llmNode.id}-${answerNode.id}`,
  //     source: llmNode.id,
  //     sourceHandle: 'source',
  //     target: answerNode.id,
  //     targetHandle: 'target',
  //   };

  //   return {
  //     nodes: toRef([startNode, llmNode, answerNode]),
  //     edges: toRef([startToLlmEdge, llmToAnswerEdge]),
  //   };
  // }
  setNodes([startNode]);
  return {
    nodes: toRef([startNode]),
    edges: toRef([]),
  };
};

export default useWorkflowTemplate;
