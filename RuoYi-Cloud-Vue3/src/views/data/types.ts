export interface RetrievalModel {
  reranking_enable: boolean;
  reranking_mode?: 'weighted_score' | 'reranking_model';
  reranking_model?: {
    reranking_provider_name?: string;
    reranking_model_name?: string;
  };
  score_threshold: number;
  score_threshold_enabled: boolean;
  top_k: number;
  search_method: 'semantic_search' | 'full_text_search' | 'hybrid_search';
  weights?: {
    weight_type?: 'customized';
    keyword_setting: { keyword_weight: number };
    vector_setting: {
      vector_weight: number;
      embedding_provider_name?: string;
      embedding_model_name?: string;
    };
  };
}

export interface KnowledgeItem {
  id?: string;
  name: string;
  data_source_type: string;
  description: string;
  embedding_available: boolean;
  embedding_model: string;
  embedding_model_provider: string;
  doc_form: string;
  app_count: number;
  word_count: number;
  document_count: number;
  permission: 'only_me' | 'all_team_members' | 'partial_members' | string;
  indexing_technique: 'economy' | 'high_quality' | null;
  retrieval_model_dict: RetrievalModel;
  [key: string]: any;
}

export interface KnowledgeForm {
  id?: string;
  name: string;
  description: string;
  permission: 'only_me' | 'all_team_members' | 'partial_members' | string;
  embedding_model: string;
  embedding_model_provider: string;
  indexing_technique: 'economy' | 'high_quality' | null;
  retrieval_model: RetrievalModel;
}

export interface DocumentForm {
  parser: string;
  original_document_id: string;
  indexing_technique: 'economy' | 'high_quality' | null;
  doc_form: 'text_model' | 'hierarchical_model' | 'qa_model';
  doc_type?: string;
  doc_metadata?: string;
  doc_language?: string;
  process_rule: {
    mode: 'automatic' | 'custom' | 'hierarchical';
    rules: {
      parent_mode?: 'paragraph' | 'full_doc';
      pre_processing_rules?: any[];
      segmentation: {
        max_tokens: number;
        separator: string;
        chunk_overlap?: number;
      };
      subchunk_segmentation?: { max_tokens: number; separator: string };
    };
  };
  file: File[];
  retrieval_model?: RetrievalModel;
  embedding_model?: string;
  embedding_model_provider?: string;
  [key: string]: any;
}

export interface DefaultModel {
  provider: string;
  status: string;
  label: DifyLabel;
  models: {
    label: DifyLabel;
    model: string;
    model_properties: { context_size: number };
    model_type: 'text-embedding' | 'rerank';
    status: string;
    [key: string]: any;
  }[];
  icon_small: DifyLabel;
  icon_large: DifyLabel;
}

export interface DocumentItem {
  id: string;
  name: string;
  indexing_status: string;
  tokens: number;
  word_count: number;
  hit_count: number;
  doc_form: 'text_model' | 'hierarchical_model' | 'qa_model';
  data_source_detail_dict: {
    upload_file: {
      id: string;
      name: string;
      mime_type: string;
      size: number;
      extension: string;
      created_at: number;
      created_by: string;
    };
  };
}

export interface PreviewItem {
  child_chunks?: PreviewItem[];
  content: string;
}
