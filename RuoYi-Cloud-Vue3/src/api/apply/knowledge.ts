import request from '@/utils/request';
import { KnowledgeForm } from '@/views/data/types';
import { AxiosProgressEvent } from 'axios';

// 获取知识库列表
export function listKnowledge(params: any) {
  return request({
    url: '/model-deploy/knowledge/list',
    method: 'get',
    params,
  });
}

// 创建空知识库
export function createEmptyKnowledge(data: { name: string }) {
  return request({
    url: '/model-deploy/knowledge/datasets',
    method: 'post',
    data,
  });
}

// 更新知识库设置
export function updateKnowledge(id: string, data: Partial<KnowledgeForm>) {
  return request({
    url: `/model-deploy/knowledge/datasets/${id}`,
    method: 'patch',
    data,
  });
}

// 获取embedding、rerank模型列表
export function listDefaultModel(type: string) {
  return request({
    url: `/model-deploy/knowledge/model-types/${type}`,
    method: 'get',
  });
}

// 删除知识库
export function deleteKnowledge(id: string) {
  return request({
    url: `/model-deploy/knowledge/datasets/${id}`,
    method: 'delete',
  });
}

// 知识库文档列表
export function listDocument(data: {
  id: string;
  keyword?: string;
  page?: number;
  limit?: number;
}) {
  const { id, ...params } = data;
  return request({
    url: `/model-deploy/documents/list/${id}`,
    method: 'get',
    params,
  });
}

// 通过文件创建文档
export function createDocumentByFile(id: string, data: FormData) {
  return request({
    url: `/model-deploy/documents/datasets/${id}/document/createByFile`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

// 通过文件更新文档
export function updateDocumentByFile(knowledgeId: string, docId: string, data: FormData) {
  return request({
    url: `/model-deploy/documents/datasets/${knowledgeId}/documents/${docId}/updateByFile`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

// 文档预览块
export function previewDocument(data: any) {
  return request({
    url: 'model-deploy/documents/datasets/indexingEstimate',
    method: 'post',
    data,
  });
}

// 删除文档
export function deleteDocument(knowledgeId: string, docId: string) {
  return request({
    url: `/model-deploy/documents/delete/${knowledgeId}/${docId}`,
    method: 'get',
  });
}

// 获取文档分段列表
export function listDocSegments(data: {
  knowledgeId: string;
  docId: string;
  keyword?: string;
  enabled?: string;
  page?: number;
  limit?: number;
}) {
  const { knowledgeId, docId, ...params } = data;
  return request({
    url: `/model-deploy/knowledge/datasets/${knowledgeId}/documents/${docId}/segments`,
    method: 'get',
    params,
  });
}

// 更新文档分段状态
export function switchDocSegments(
  knowledgeId: string,
  docId: string,
  segmentId: string,
  action: 'enable' | 'disable',
) {
  return request({
    url: `/model-deploy/knowledge/datasets/${knowledgeId}/documents/${docId}/segment/${action}?segmentId=${segmentId}`,
    method: 'put',
  });
}

// 文档解析
export function getParsedDocument(documentId: string) {
  return request({
    url: `/model-deploy/parsedDocuments/${documentId}`,
    method: 'get',
  });
}

// 解析后文件上传
export function uploadParsedDocument(data: FormData) {
  return request({
    url: `/model-deploy/documents/upload`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

// 文档解析
export function parsedDocuments(data: FormData) {
  return request({
    url: `/model-deploy/parsedDocuments/upload?parsedType=mineru`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

// 模型参数规则
export function getParameterRules(provider: string, model: string) {
  return request({
    url: `/model-deploy/knowledge/model-providers/${provider}/models/parameter-rules?model=${model}`,
    method: 'get',
  });
}

// 获取文档嵌入状态
export function getDocumentEmbeddingStatus(knowledgeId: string, batch: string) {
  return request({
    url: `/model-deploy/documents/datasets/${knowledgeId}/documents/${batch}/indexingStatus`,
    method: 'get',
  });
}
