<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.dream</groupId>
    <artifactId>blade-model</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    <name>dream</name>
    <url>http://www.dream.vip</url>
    <description>eternal</description>

    <properties>
        <dream.version>1.0.0</dream.version>
        <spring.version>3.3.5</spring.version>
        <hetuall.version>5.8.24</hetuall.version>
        <fileupload.version>1.4</fileupload.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>17</java.version>
        <maven-jar-plugin.version>3.1.1</maven-jar-plugin.version>
        <mybatis-spring-boot.version>3.0.3</mybatis-spring-boot.version>
        <druid.version>1.2.23</druid.version>
        <bitwalker.version>1.21</bitwalker.version>
        <kaptcha.version>2.3.3</kaptcha.version>
        <pagehelper.boot.version>2.1.0</pagehelper.boot.version>
        <fastjson.version>2.0.53</fastjson.version>
        <oshi.version>6.6.5</oshi.version>
        <commons.io.version>2.14.0</commons.io.version>
        <poi.version>5.4.0</poi.version>
        <velocity.version>2.3</velocity.version>
        <jwt.version>0.9.1</jwt.version>
        <mysqlj.version>8.2.0</mysqlj.version>
        <jaxb-api.version>2.3.1</jaxb-api.version>
        <jakarta.version>6.0.0</jakarta.version>
        <knife4j.version>4.4.0</knife4j.version>
        <lombok.version>1.18.34</lombok.version>
        <okhttp.version>4.12.0</okhttp.version>
        <retrofit2.version>2.9.0</retrofit2.version>
        <httpclient.version>4.5.14</httpclient.version>
        <jtokkit.version>0.6.1</jtokkit.version>
        <jsonschema.version>1.0.34</jsonschema.version>
        <docx4j.version>6.1.2</docx4j.version>
        <spring-mock.version>2.0.8</spring-mock.version>
        <dysmsapi.version>3.1.0</dysmsapi.version>
        <!-- OSS 配置 -->
        <aws-java-sdk-s3.version>1.12.400</aws-java-sdk-s3.version>
        <minio.version>8.0.0</minio.version>
    </properties>
    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

            <!-- SpringBoot的依赖配置-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 阿里数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-3-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- 解析客户端操作系统、浏览器等 -->
            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${bitwalker.version}</version>
            </dependency>

            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
            </dependency>
            <!-- MyBatis集成 -->
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis-spring-boot.version}</version>
            </dependency>
            <!-- MySQL驱动 -->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysqlj.version}</version>
            </dependency>
            <!-- XML/API支持 -->
            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>${jaxb-api.version}</version>
            </dependency>
            <!-- Servlet规范 -->
            <dependency>
                <groupId>jakarta.servlet</groupId>
                <artifactId>jakarta.servlet-api</artifactId>
                <version>${jakarta.version}</version>
            </dependency>

            <!-- 系统信息监控 -->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>${oshi.version}</version>
            </dependency>

            <!-- io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!-- Excel处理 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- velocity代码生成使用模板模板引擎 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- 阿里JSON解析器 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- Token生成与解析-->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>

            <!-- 验证码 -->
            <dependency>
                <groupId>pro.fessional</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <!-- 定时任务-->
            <dependency>
                <groupId>cn.dream</groupId>
                <artifactId>dream-quartz</artifactId>
                <version>${dream.version}</version>
            </dependency>

            <!-- 代码生成-->
            <dependency>
                <groupId>cn.dream</groupId>
                <artifactId>dream-generator</artifactId>
                <version>${dream.version}</version>
            </dependency>

            <!-- 核心模块-->
            <dependency>
                <groupId>cn.dream</groupId>
                <artifactId>dream-framework</artifactId>
                <version>${dream.version}</version>
            </dependency>

            <!-- 系统模块-->
            <dependency>
                <groupId>cn.dream</groupId>
                <artifactId>dream-system</artifactId>
                <version>${dream.version}</version>
            </dependency>
            <!--rest请求模块-->
            <dependency>
                <groupId>cn.dream</groupId>
                <artifactId>dream-rest</artifactId>
                <version>${dream.version}</version>
            </dependency>
            <!--刀线图模块-->
            <dependency>
                <groupId>cn.dream.blade</groupId>
                <artifactId>dream-blade</artifactId>
                <version>${dream.version}</version>
            </dependency>
            <!--对象存储-->
            <dependency>
                <groupId>cn.dream.oss</groupId>
                <artifactId>dream-oss</artifactId>
                <version>${dream.version}</version>
            </dependency>
            <!-- 通用工具-->
            <dependency>
                <groupId>cn.dream</groupId>
                <artifactId>dream-common</artifactId>
                <version>${dream.version}</version>
            </dependency>
            <!-- 接口文档 -->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>
            <!-- 代码简化 -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <!-- HTTP客户端 -->
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp-sse</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
            <!-- HTTP请求库 -->
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
            <!-- HTTP请求库 -->
            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>retrofit</artifactId>
                <version>${retrofit2.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.squareup.okhttp3</groupId>
                        <artifactId>okhttp</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--RxJava2适配器-->
            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>adapter-rxjava2</artifactId>
                <version>${retrofit2.version}</version>
            </dependency>
            <!--Jackson数据转换器-->
            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>converter-jackson</artifactId>
                <version>${retrofit2.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.squareup.okhttp3</groupId>
                        <artifactId>okhttp</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--HttpClient（网络通信工具）-->
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <!--JTokkit（OpenAI模型专用分词器）-->
            <dependency>
                <groupId>com.knuddels</groupId>
                <artifactId>jtokkit</artifactId>
                <version>${jtokkit.version}</version>
            </dependency>
            <!--JSON Schema生成工具-->
            <dependency>
                <groupId>com.kjetland</groupId>
                <artifactId>mbknor-jackson-jsonschema_2.12</artifactId>
                <version>${jsonschema.version}</version>
            </dependency>
            <!--Word文档处理引擎（docx4j）-->
            <dependency>
                <groupId>org.docx4j</groupId>
                <artifactId>docx4j</artifactId>
                <version>${docx4j.version}</version>
            </dependency>
            <!--Spring测试模拟框架-->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-mock</artifactId>
                <version>${spring-mock.version}</version>
            </dependency>
            <!-- 阿里云短信服务 -->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dysmsapi20170525</artifactId>
                <version>${dysmsapi.version}</version>
            </dependency>
            <!-- OSS对象存储模块 -->
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${aws-java-sdk-s3.version}</version>
            </dependency>
            <!-- 对象存储 -->
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>
            <!-- 全能工具包 -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hetuall.version}</version>
            </dependency>
            <!-- 文件上传 -->
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${fileupload.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>4.5.1</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <modules>
        <module>dream-admin</module>
        <module>dream-framework</module>
        <module>dream-system</module>
        <module>dream-quartz</module>
        <module>dream-generator</module>
        <module>dream-common</module>
        <module>dream-rest</module>
        <module>dream-oss</module>
        <module>dream-blade</module>
    </modules>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.13.0</version>
                <configuration>
                    <parameters>true</parameters>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>3.3.0</version>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>naii-repo</id>
            <url>http://192.168.20.2:30121/repository/naii-maven-group/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>
    <distributionManagement>
        <!-- 发布 Release 版本时使用的仓库 -->
        <repository>
            <id>naii-releases</id>
            <url>http://192.168.20.2:30121/repository/naii-maven-local-release/</url>
        </repository>

        <!-- 发布 Snapshot 版本时使用的仓库 -->
        <snapshotRepository>
            <id>naii-snapshots</id>
            <url>http://192.168.20.2:30121/repository/naii-maven-local-snapshot/</url>
        </snapshotRepository>
        <!-- 项目的网站信息 -->
        <site>
            <id>naii-site</id>
            <url>http://www.example.com/site</url>
        </site>
    </distributionManagement>
</project>
